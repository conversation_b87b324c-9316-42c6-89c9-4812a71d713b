{"openapi": "3.0.0", "info": {"title": "authentication-facade", "version": "1.0.0", "description": "authentication-facade", "contact": {}}, "paths": {"/auth/forget-password/verify": {"post": {"x-controller-name": "ForgetPasswordController", "x-operation-name": "updatePassword", "tags": ["ForgetPasswordController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Success Response."}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "description": "", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordDto"}}}}, "operationId": "ForgetPasswordController.updatePassword"}}, "/auth/forget-password": {"post": {"x-controller-name": "ForgetPasswordController", "x-operation-name": "forgetPassword", "tags": ["ForgetPasswordController"], "responses": {"204": {"description": "Success Response."}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "description": "", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgetPasswordRequestDto"}}}}, "operationId": "ForgetPasswordController.forgetPassword"}}, "/auth/login": {"post": {"x-controller-name": "LoginController", "x-operation-name": "login", "tags": ["LoginController"], "description": "Gets you the otp success message that that means the otp has been sent to user email succesfully", "responses": {"200": {"description": "'Auth Code that you can use to generate access\n          and refresh tokens using the POST /auth/token API'", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "operationId": "LoginController.login"}}, "/auth/logout": {"post": {"x-controller-name": "LoginController", "x-operation-name": "logout", "tags": ["LoginController"], "security": [{"HTTPBearer": []}], "description": "Logs out the user by invalidating the access and refresh tokens", "responses": {"204": {"description": "Logout successful"}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "parameters": [{"name": "authorization", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "operationId": "LoginController.logout"}}, "/auth/me": {"get": {"x-controller-name": "LoginController", "x-operation-name": "me", "tags": ["LoginController"], "security": [{"HTTPBearer": []}], "description": "To get the user details", "responses": {"200": {"description": "User Object", "content": {}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "operationId": "LoginController.me"}}, "/auth/set-password/verify": {"post": {"x-controller-name": "SetPasswordController", "x-operation-name": "setPassword", "tags": ["SetPasswordController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Success Response."}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "description": "", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordDto"}}}}, "operationId": "SetPasswordController.setPassword"}}, "/auth/token": {"post": {"x-controller-name": "LoginController", "x-operation-name": "getToken", "tags": ["LoginController"], "description": "'Send the code received from the OTP verify api\n        and get refresh token and access token (webapps)'", "responses": {"200": {"description": "Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthTokenRequest"}}}}, "operationId": "LoginController.getToken"}}, "/auth/token-refresh": {"post": {"x-controller-name": "LoginController", "x-operation-name": "exchangeToken", "tags": ["LoginController"], "security": [{"HTTPBearer": []}], "description": "Gets you a new access and refresh token once your access token is expired", "responses": {"200": {"description": "New Token Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "operationId": "LoginController.exchangeToken"}}, "/ping": {"get": {"x-controller-name": "PingController", "x-operation-name": "ping", "tags": ["PingController"], "responses": {"200": {"description": "Ping Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PingResponse"}}}}}, "description": "", "operationId": "PingController.ping"}}, "/roles": {"get": {"x-controller-name": "RoleController", "x-operation-name": "find", "tags": ["RoleController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Roles", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "RoleController.find"}}, "/tenants/role-view/count": {"get": {"x-controller-name": "RoleViewController", "x-operation-name": "getRolesCount", "tags": ["RoleViewController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Get Roles view count", "content": {"application/json": {"schema": {"type": "object", "properties": {"count": {"type": "number"}}}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}, "permission": ["6"]}, "description": "\n\n| Permissions |\n| ------- |\n| 6   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "v_roles.Where<PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<RoleView>"}}}}], "operationId": "RoleViewController.getRolesCount"}}, "/tenants/role-view": {"get": {"x-controller-name": "RoleViewController", "x-operation-name": "getRoles", "tags": ["RoleViewController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Get Roles", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RoleView"}, "type": "array"}}}}, "400": {"description": "The syntax of the request entity is incorrect."}, "401": {"description": "Invalid Credentials."}, "404": {"description": "The entity requested does not exist."}, "422": {"description": "The syntax of the request entity is incorrect"}, "permission": ["6"]}, "description": "\n\n| Permissions |\n| ------- |\n| 6   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/v_roles.Filter"}}}}], "operationId": "RoleViewController.getRoles"}}, "/users/bulk": {"post": {"x-controller-name": "UserController", "x-operation-name": "createUsersBulk", "tags": ["UserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Bulk create Users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserViewPartial"}}}}}, "409": {"description": "Duplicate users found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "duplicateUsers": {"type": "array", "items": {"type": "string"}}}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 13   |\n", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Create bulk new users"}}}}}, "operationId": "UserController.createUsersBulk"}}, "/users/count": {"get": {"x-controller-name": "UserController", "x-operation-name": "getUserCount", "tags": ["UserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "count of Users", "content": {"application/json": {"schema": {"type": "object", "properties": {"count": {"type": "number"}}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 12   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "v_users.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserView>"}}}}], "operationId": "UserController.getUserCount"}}, "/users/user-list": {"get": {"x-controller-name": "UserController", "x-operation-name": "getUserLists", "tags": ["UserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 12   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/v_users.Filter"}}}}], "operationId": "UserController.getUserLists"}}, "/users/{id}/status": {"patch": {"x-controller-name": "UserController", "x-operation-name": "updateUserStatus", "tags": ["UserController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Update user status"}}, "description": "\n\n| Permissions |\n| ------- |\n| 14   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStatusRequestDto"}}}, "x-parameter-index": 1}, "operationId": "UserController.updateUserStatus"}}, "/users/{id}": {"patch": {"x-controller-name": "UserController", "x-operation-name": "updateUser", "tags": ["UserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Update the Users", "content": {"application/json": {"schema": {"type": "object", "items": {"$ref": "#/components/schemas/UserViewPartial", "definitions": {"UserViewPartial": {"$ref": "#/components/schemas/UserViewPartial"}}}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 14   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Update User"}}}, "x-parameter-index": 1}, "operationId": "UserController.updateUser"}, "get": {"x-controller-name": "UserController", "x-operation-name": "getUserById", "tags": ["UserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Get User by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserViewPartial"}}}}, "404": {"description": "User not found"}}, "description": "\n\n| Permissions |\n| ------- |\n| 12   |\n| 6   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "UserController.getUserById"}}, "/users": {"get": {"x-controller-name": "UserController", "x-operation-name": "find", "tags": ["UserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 12   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "UserController.find"}}, "/": {"get": {"x-controller-name": "HomePageController", "x-operation-name": "homePage", "tags": ["HomePageController"], "responses": {"200": {"description": "Home Page", "content": {"text/html": {"schema": {"type": "string"}}}}}, "description": "", "operationId": "HomePageController.homePage"}}}, "components": {"securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"User": {"title": "User", "type": "object", "description": "This is signature for user model.", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "designation": {"type": "string"}, "phone": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string", "format": "date-time"}, "photoUrl": {"type": "string"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "dob": {"type": "string", "format": "date-time"}, "defaultTenantId": {"type": "string"}}, "required": ["firstName", "username", "email"], "additionalProperties": false}, "Update User": {"title": "Update User", "type": "object", "description": "DTO for adding a user (tsType: Omit<Partial<CreateUserRequestDto>, 'email'>, schemaOptions: { title: 'Update User', partial: true, exclude: [ 'email' ] })", "properties": {"fullName": {"type": "string", "minLength": 7, "maxLength": 100, "pattern": "^[A-Za-z ]+$", "errorMessage": {"minLength": "Full Name should have at least 7 characters", "maxLength": "Full Name should have at most 100 characters", "pattern": "Full Name should only contain letters and spaces"}}, "roleId": {"type": "string"}}, "additionalProperties": false}, "UserViewPartial": {"title": "UserViewPartial", "type": "object", "description": "User details view in DB (tsType: Partial<UserView>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "middleName": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string"}, "designation": {"type": "string"}, "phone": {"type": "string"}, "authClientIds": {"type": "string"}, "lastLogin": {"type": "string"}, "photoUrl": {"type": "string"}, "gender": {"type": "string", "description": "This field takes a single character as input in database.\n    'M' for male and 'F' for female.", "enum": ["M", "F", "O"]}, "dob": {"type": "string", "format": "date-time", "nullable": true}, "defaultTenantId": {"type": "string"}, "status": {"type": "number", "maximum": 11, "minimum": 0}, "tenantId": {"type": "string"}, "roleId": {"type": "string"}, "tenantName": {"type": "string"}, "tenantKey": {"type": "string"}, "roleName": {"type": "string"}, "userTenantId": {"type": "string"}}, "additionalProperties": false}, "UpdateUserStatusRequestDto": {"title": "UpdateUserStatusRequestDto", "type": "object", "description": "DTO for updating the status of a user. UserStatus mapping: 1=ACTIVE, 2=INACTIVE (tsType: UpdateUserStatusRequestDto, schemaOptions: { title: 'UpdateUserStatusRequestDto' })", "properties": {"status": {"type": "number", "enum": [1, 2], "description": "UserStatus: 1=ACTIVE, 2=INACTIVE", "errorMessage": {"enum": "Status must be a valid UserStatus integer value: 1=ACTIVE, 2=INACTIVE"}}}, "required": ["status"], "additionalProperties": false}, "Create bulk new users": {"title": "Create bulk new users", "type": "object", "description": "DTO for adding a user (tsType: CreateUserRequestDto, schemaOptions: { title: 'Create bulk new users' })", "properties": {"fullName": {"type": "string", "minLength": 7, "maxLength": 100, "pattern": "^[A-Za-z ]+$", "errorMessage": {"minLength": "Full Name should have at least 7 characters", "maxLength": "Full Name should have at most 100 characters", "pattern": "Full Name should only contain letters and spaces"}}, "email": {"type": "string", "minLength": 5, "maxLength": 254, "format": "email", "errorMessage": {"minLength": "Email Address should have at least 5 characters", "maxLength": "Email Address should have at most 254 characters", "format": "Email Address is not in a valid format"}}, "roleId": {"type": "string"}}, "required": ["fullName", "email", "roleId"], "additionalProperties": false}, "UpdatePasswordDto": {"title": "UpdatePasswordDto", "type": "object", "description": "DTO describing the payload used to update the password during the forget password flow for a tenant.", "properties": {"newPassword": {"type": "string"}}, "required": ["newPassword"], "additionalProperties": false}, "Role": {"title": "Role", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "roleType": {"type": "number", "maximum": 15, "minimum": 0}, "description": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "allowedClients": {"type": "array", "items": {"type": "string"}}, "tenantId": {"type": "string"}}, "required": ["name", "tenantId"], "additionalProperties": false}, "RoleView": {"title": "Role<PERSON>iew", "type": "object", "description": "User details view in DB", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "roleId": {"type": "string"}, "roleName": {"type": "string"}, "status": {"type": "number"}, "tenantId": {"type": "string"}, "userCount": {"type": "number"}}, "required": ["roleId", "tenantId", "userCount"], "additionalProperties": false}, "LoginRequest": {"title": "LoginRequest", "type": "object", "description": "This is the signature for login request.", "properties": {"username": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "password": {"type": "string", "description": "This property is supposed to be a string and is a required field"}}, "required": ["username", "password"], "additionalProperties": false}, "TokenResponse": {"title": "TokenResponse", "type": "object", "description": "This is signature for successful token response.", "properties": {"accessToken": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "refreshToken": {"type": "string", "description": "This property is supposed to be a string and is a required field"}, "expires": {"type": "number"}, "pubnubToken": {"type": "string"}}, "required": ["accessToken", "refreshToken", "expires"], "additionalProperties": false}, "AuthTokenRequest": {"title": "AuthTokenRequest", "type": "object", "description": "This is the signature for requesting the accessToken and refreshToken.", "properties": {"code": {"type": "string"}}, "required": ["code"], "additionalProperties": false}, "RefreshTokenRequest": {"title": "RefreshTokenRequest", "type": "object", "properties": {"refreshToken": {"type": "string"}}, "required": ["refreshToken"], "additionalProperties": false}, "ForgetPasswordRequestDto": {"title": "ForgetPasswordRequestDto", "type": "object", "description": "model describing payload used to send forget password email for a tenant", "properties": {"email": {"type": "string"}}, "required": ["email"], "additionalProperties": false}, "PingResponse": {"type": "object", "title": "PingResponse", "properties": {"greeting": {"type": "string"}, "date": {"type": "string"}, "url": {"type": "string"}, "headers": {"type": "object", "properties": {"Content-Type": {"type": "string"}}, "additionalProperties": true}}}, "v_roles.Filter": {"type": "object", "title": "v_roles.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "v_roles.Where<PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "roleId": {"type": "boolean"}, "roleName": {"type": "boolean"}, "status": {"type": "boolean"}, "tenantId": {"type": "boolean"}, "userCount": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "roleId", "<PERSON><PERSON><PERSON>", "status", "tenantId", "userCount"], "example": "deleted"}, "uniqueItems": true}], "title": "v_roles.<PERSON>"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<RoleView>"}, "v_users.Filter": {"type": "object", "title": "v_users.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "v_users.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "firstName": {"type": "boolean"}, "lastName": {"type": "boolean"}, "middleName": {"type": "boolean"}, "username": {"type": "boolean"}, "email": {"type": "boolean"}, "designation": {"type": "boolean"}, "phone": {"type": "boolean"}, "authClientIds": {"type": "boolean"}, "lastLogin": {"type": "boolean"}, "photoUrl": {"type": "boolean"}, "gender": {"type": "boolean"}, "dob": {"type": "boolean"}, "defaultTenantId": {"type": "boolean"}, "status": {"type": "boolean"}, "tenantId": {"type": "boolean"}, "roleId": {"type": "boolean"}, "tenantName": {"type": "boolean"}, "tenantKey": {"type": "boolean"}, "roleName": {"type": "boolean"}, "userTenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "firstName", "lastName", "middleName", "username", "email", "designation", "phone", "authClientIds", "lastLogin", "photoUrl", "gender", "dob", "defaultTenantId", "status", "tenantId", "roleId", "tenantName", "tenantKey", "<PERSON><PERSON><PERSON>", "userTenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "v_users.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<UserView>"}}}, "servers": [{"url": "/"}]}