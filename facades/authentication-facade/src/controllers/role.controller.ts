import {inject} from '@loopback/context';
import {get, getModelSchemaRef, param, RestBindings} from '@loopback/rest';
import {Filter} from '@loopback/repository';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {PermissionKey} from '@local/core';
import {authorize} from 'loopback4-authorization';
import {RoleHelperService} from '../services';
import {Role} from '@sourceloop/user-tenant-service';
const baseUrl = '/roles';

/**
 * Controller for handling role-related endpoints.
 * Provides APIs to fetch roles.
 */
export class RoleController {
  /**
   * Constructs a new RoleController.
   * @param roleHelperService - Service for role-related logic.
   * @param request - The current HTTP request object.
   */
  constructor(
    @inject('services.RoleHelperService')
    private readonly roleHelperService: RoleHelperService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  /**
   * Retrieves a list of roles.
   * Requires bearer authentication and appropriate permissions.
   * @param filter - Optional filter criteria for querying roles.
   * @returns A promise resolving to an array of Role objects.
   */
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @authorize({
    permissions: [PermissionKey.ViewRoles],
  })
  @get(baseUrl, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Roles',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Role)},
          },
        },
      },
    },
  })
  async find(
    @param.query.object('filter') filter?: Filter<Role>,
  ): Promise<Role[]> {
    // Delegate the role retrieval to the roleHelperService
    return this.roleHelperService.getRoles(filter);
  }
}
