import {BindingScope, inject, injectable} from '@loopback/context';
import {AnyObject, Count, Filter, Where} from '@loopback/repository';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {User, UserView} from '@sourceloop/user-tenant-service';
import {
  IUserView,
  NotificationProxyService,
  UserTenantProxyService,
} from './proxies';
import {CreateUserRequestDto, CreateUserOnboardDto} from '../models/dto';
import {
  NotificationEventType,
  NotificationType,
  PermissionKey,
} from '@local/core';
import {service} from '@loopback/core';
import {LOGGER, ILogger, UserStatus} from '@sourceloop/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {Notification} from '../models';
import {AuthenticatorService} from './authenticator.service';
import {CryptoHelperService} from './crypto-helper.service';
import {TemplateService} from './template.service';
import {UpdateUserStatusRequestDto} from '../models/dto/update-user-status-req.dto.model';

/**
 * Service to assist with user-related operations, such as fetching user lists and counts,
 * while handling authorization and filtering out super admin users.
 */
@injectable({scope: BindingScope.TRANSIENT})
export class UserHelperService {
  errorMessageForTenantIdNotDefined =
    'DEFAULT_TENANT_ID is not defined in environment variables';
  /**
   * The authorization token extracted from the request headers.
   */
  token: string;

  /**
   * Constructs a new UserHelperService.
   * Extracts the authorization token from the request headers and injects all required services.
   * @param request - The current HTTP request object.
   * @param userProxyService - The proxy service for user operations.
   * @param cryptoHelperService - Service for cryptographic operations.
   * @param authenticatorService - Service for authentication logic.
   * @param notificationProxyService - Service for sending notifications.
   * @param templateService - Service for email template management.
   * @param currentUser - The current authenticated user context.
   * @param logger - Logger instance for logging.
   * @throws {HttpErrors.Unauthorized} If the authorization header is missing.
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.UserTenantProxyService')
    private readonly userProxyService: UserTenantProxyService,
    @service(CryptoHelperService)
    private readonly cryptoHelperService: CryptoHelperService,
    @service(AuthenticatorService)
    private readonly authenticatorService: AuthenticatorService,
    @inject(`services.NotificationProxyService`)
    private readonly notificationProxyService: NotificationProxyService,
    @service(TemplateService)
    private readonly templateService: TemplateService,
    @inject(AuthenticationBindings.CURRENT_USER)
    private readonly currentUser: IAuthUserWithPermissions,
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
  ) {
    // Extract the authorization token from the request headers
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    } else {
      // Throw unauthorized error if token is missing
      throw new HttpErrors.Unauthorized();
    }
  }

  /**
   * Retrieves the count of users, excluding those with the super admin role.
   * @param where - Optional filter criteria for users.
   * @returns A promise resolving to the count of users.
   * @throws {HttpErrors.InternalServerError} If DEFAULT_TENANT_ID is not set.
   */
  async getUsersCount(where?: Where<UserView>): Promise<Count> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        this.errorMessageForTenantIdNotDefined,
      );
    }
    // Prepare filter to exclude super admin users from the count
    const filterwhere = (where ?? {}) as AnyObject;
    const whereObj = {
      and: [
        ...(filterwhere.and ?? (filterwhere ? [filterwhere] : [])),
        {roleName: {neq: process.env.SUPER_ADMIN_ROLE_NAME!}},
      ],
    };
    const jsonWhere = JSON.stringify(whereObj);
    // Delegate the count operation to the UserProxyService
    return this.userProxyService.getUserCount(
      this.token,
      process.env.DEFAULT_TENANT_ID,
      jsonWhere,
    );
  }

  /**
   * Retrieves a list of users, excluding those with the super admin role.
   * Throws InternalServerError if DEFAULT_TENANT_ID is not set.
   * @param filter - Optional filter criteria for querying users.
   * @returns A promise resolving to an array of UserView objects.
   * @throws {HttpErrors.InternalServerError} If DEFAULT_TENANT_ID is not set.
   */
  async getUserLists(filter?: Filter<UserView>): Promise<UserView[]> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        this.errorMessageForTenantIdNotDefined,
      );
    }
    // Prepare filter to exclude super admin users from the list
    const filterWhere = (filter?.where ?? {}) as AnyObject;
    const refinedFilter: Filter<UserView> = {
      ...filter,
      where: {
        and: [
          ...(filterWhere.and ?? (filter?.where ? [filter.where] : [])),
          {roleName: {neq: process.env.SUPER_ADMIN_ROLE_NAME!}},
        ],
      },
    };
    // Extract the access token part from the authorization header
    const accessToken = this.token?.split(' ')[1];
    // Delegate the list operation to the UserProxyService
    return this.userProxyService.getUsersList(
      accessToken,
      process.env.DEFAULT_TENANT_ID,
      refinedFilter,
    );
  }

  /**
   * Retrieves a user by their unique identifier.
   * Throws NotFound if the user does not exist.
   * @param userId - The unique identifier of the user.
   * @returns The user view object if found.
   * @throws {HttpErrors.InternalServerError} If DEFAULT_TENANT_ID is not set.
   * @throws {HttpErrors.NotFound} If user is not found.
   */
  async getUserById(userId: string): Promise<UserView | undefined> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        this.errorMessageForTenantIdNotDefined,
      );
    }
    // Build filter to find user by id
    const refinedFilter: Filter<UserView> = {
      where: {
        and: [{id: {eq: userId}}],
      },
    };
    // Query user list from proxy service
    const users = await this.userProxyService.getUsersList(
      this.token?.split(' ')[1],
      process.env.DEFAULT_TENANT_ID,
      refinedFilter,
    );
    if (!users || users.length === 0) {
      throw new HttpErrors.NotFound(`User with id ${userId} not found`);
    }
    return users[0];
  }

  /**
   * Updates a user's details by their unique identifier.
   * Throws NotFound if the user does not exist.
   * @param userId - The unique identifier of the user.
   * @param dto - The update request DTO containing new user details.
   * @returns The updated user view object.
   * @throws {HttpErrors.InternalServerError} If DEFAULT_TENANT_ID is not set.
   * @throws {HttpErrors.NotFound} If user is not found.
   */
  async updateUser(
    userId: string,
    dto: Partial<Omit<CreateUserRequestDto, 'email'>>,
  ): Promise<UserView> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        this.errorMessageForTenantIdNotDefined,
      );
    }

    const updateToken = this.token?.split(' ')[1];
    // Find the user to update
    const users = await this.userProxyService.getUsersList(
      updateToken,
      process.env.DEFAULT_TENANT_ID,
      {
        where: {
          id: userId,
        },
      },
    );

    if (!users || users.length === 0) {
      throw new HttpErrors.NotFound(`User with id ${userId} not found`);
    }

    // Prepare the DTO for update
    const newDto: Partial<Omit<CreateUserOnboardDto, 'email'>> = {
      firstName: dto.fullName,
      roleId: dto.roleId,
    };

    // Update the user via the proxy service
    await this.userProxyService.updateUser(
      this.token,
      process.env.DEFAULT_TENANT_ID,
      userId,
      newDto,
    );

    // Retrieve the updated user
    const updatedUsers = await this.userProxyService.getUsersList(
      updateToken,
      process.env.DEFAULT_TENANT_ID,
      {
        where: {
          id: userId,
        },
      },
    );

    const updatedUser = updatedUsers[0];

    return updatedUser;
  }

  /**
   * Creates multiple users in bulk.
   * Throws Conflict if duplicate emails are found in the input.
   * @param dtos - Array of user creation request DTOs.
   * @returns Array of created users or error with duplicate users.
   * @throws {HttpErrors.InternalServerError} If DEFAULT_TENANT_ID is not set.
   * @throws {HttpErrors.Conflict} If duplicate emails are found.
   */
  async createBulkUsers(
    dtos: CreateUserRequestDto[],
  ): Promise<User[] | {error: string; duplicateUsers: string[]}> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        this.errorMessageForTenantIdNotDefined,
      );
    }

    // Check for duplicate emails in input DTOs
    const emailCount: Record<string, number> = {};
    for (const dto of dtos) {
      const email = dto.email?.toLowerCase().trim();
      if (email) {
        emailCount[email] = (emailCount[email] || 0) + 1;
      }
    }
    const duplicateUsers = Object.keys(emailCount).filter(
      email => emailCount[email] > 1,
    );
    if (duplicateUsers.length > 0) {
      const error = new HttpErrors.Conflict('Duplicate emails found in input');
      error.duplicateUsers = duplicateUsers;
      throw error;
    }

    // Map DTOs to onboard DTOs for user creation
    const newDtos = dtos.map(
      dto =>
        new CreateUserOnboardDto({
          ...dto,
          firstName: dto.fullName,
          username: dto.email,
        }),
    );

    // Create users via proxy service
    const users = await this.userProxyService.createBulkUsers(
      this.token,
      process.env.DEFAULT_TENANT_ID,
      newDtos,
    );

    // Send notification to all created users
    await this.sendNotification(users as unknown as User[]);
    return users;
  }

  /**
   * Sends notification emails to the provided users.
   * Generates a temporary token and uses a template for the notification.
   * Throws InternalServerError if template is not found or environment is not set.
   * @param users - Array of users to notify.
   * @throws {HttpErrors.InternalServerError} If DEFAULT_TENANT_ID or template is not set.
   * @throws {HttpErrors.NotFound} If a user is not found.
   */
  async sendNotification(users: User[]): Promise<void> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        this.errorMessageForTenantIdNotDefined,
      );
    }

    // Generate a temporary token for notification
    const token = this.cryptoHelperService.generateTempToken({
      userTenantId: process.env.CLIENT_ID,
      tenantId: process.env.DEFAULT_TENANT_ID,
      defaultTenantId: process.env.DEFAULT_TENANT_ID,
      permissions: [
        PermissionKey.UpdatePassword,
        PermissionKey.CreateNotification,
        PermissionKey.ViewTenantUser,
        PermissionKey.ViewNotificationTemplate,
      ],
    });

    const baseSetLink = process.env.SET_PASSWORD_LINK ?? '';

    // Fetch notification template
    const template = await this.notificationProxyService.getTemplateByName(
      NotificationEventType.UserAdded,
      NotificationType.EMAIL,
      token,
    );
    if (!template) {
      throw new HttpErrors.InternalServerError(
        'Notification template for add user not found',
      );
    }

    // Prepare notifications for all users
    const notifications: Notification[] = await Promise.all(
      users.map(async user => {
        // Generate and store temporary token for each user
        const userView = await this.getUserById(user.id ?? '');
        if (!userView) {
          throw new HttpErrors.NotFound(`User with id ${user.id} not found`);
        }
        const key =
          await this.authenticatorService.setForgetPasswordTempToken(userView);

        // Construct reset link
        const resetLinkWithCode = `${baseSetLink}?code=${encodeURIComponent(key)}`;

        // Generate email content using template
        const emailBody = this.templateService.generateEmail(template.body, {
          PROFILE_SETUP_LINK: resetLinkWithCode,
        });

        return new Notification({
          subject: template.subject,
          body: emailBody,
          receiver: {
            to: [
              {
                id: user.id ?? '',
                toEmail: user.email,
              },
            ],
          },
          type: 1,
          sentDate: new Date(),
          options: {
            fromEmail: process.env.NOTIFICATION_FROM_EMAIL,
          },
        });
      }),
    );

    // Send notifications and log any failures
    const results = await Promise.allSettled(
      notifications.map(notification =>
        this.notificationProxyService.createNotification(token, notification),
      ),
    );
    const failed = results
      .map((result, idx) => ({result, notification: notifications[idx]}))
      .filter(item => item.result.status === 'rejected');
    if (failed.length > 0) {
      this.logger.error(
        `Error sending notification failed:`,
        JSON.stringify(failed),
      );
    }
  }

  /**
   * Updates the status of a user by their unique identifier.
   * If status changes from INACTIVE to ACTIVE, sets to REGISTERED and sends notification.
   * @param userId - The unique identifier of the user.
   * @param updateUserStatusRequest - DTO containing the new status.
   * @throws {HttpErrors.InternalServerError} If DEFAULT_TENANT_ID is not set.
   * @throws {HttpErrors.NotFound} If user is not found.
   */
  async updateUserStatus(
    userId: string,
    updateUserStatusRequest: UpdateUserStatusRequestDto,
  ): Promise<void> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        this.errorMessageForTenantIdNotDefined,
      );
    }

    const updateToken = this.token?.split(' ')[1];
    // Find the user to update
    const users = await this.userProxyService.getUsersList(
      updateToken,
      process.env.DEFAULT_TENANT_ID,
      {
        where: {
          id: userId,
        },
      },
    );

    if (!users || users.length === 0) {
      throw new HttpErrors.NotFound(`User with id ${userId} not found`);
    }

    let updateStatus = updateUserStatusRequest.status;
    let isEmailSend = false;

    // If status changes from INACTIVE to ACTIVE, set to REGISTERED and send notification
    if (
      (users[0] as unknown as IUserView).status === UserStatus.INACTIVE &&
      updateStatus === UserStatus.ACTIVE
    ) {
      isEmailSend = true;
      updateStatus = UserStatus.REGISTERED;
    }

    // Update user status via proxy service
    await this.userProxyService.updateUserStatus(
      this.token,
      process.env.DEFAULT_TENANT_ID,
      userId,
      new UpdateUserStatusRequestDto({
        status: updateStatus,
      }),
    );

    // Send notification if status was changed to REGISTERED
    if (isEmailSend) {
      await this.sendNotification(users as unknown as User[]);
    }
  }
}
