import {inject, Provider} from '@loopback/core';
import {Count, Filter} from '@loopback/repository';
import {getService} from '@loopback/service-proxy';
import {Gender, UserStatus} from '@sourceloop/core';
import {UserTenantServiceDataSource} from '../../datasources';
import {Role, User, UserView} from '@sourceloop/user-tenant-service';
import {RoleView} from '@local/core';
import {CreateUserOnboardDto} from '../../models/dto';
import {UpdateUserStatusRequestDto} from '../../models/dto/update-user-status-req.dto.model';

export interface IUserView {
  id?: string;
  firstName: string;
  lastName?: string;
  middleName?: string;
  username: string;
  email: string;
  designation?: string;
  phone?: string;
  authClientIds: string;
  lastLogin?: string;
  photoUrl?: string;
  gender?: Gender; // 'M' | 'F' | 'O'
  dob?: Date;
  defaultTenantId: string;
  status?: UserStatus; // 0 to 11 (per your jsonSchema)
  tenantId: string;
  roleId: string;
  tenantName: string;
  tenantKey?: string;
  roleName?: string;
  userTenantId: string;
}

/**
 * Interface defining the contract for the UserTenantProxyService.
 *
 * Provides methods to interact with the user-tenant service for finding, creating, updating, and managing tenant users and roles.
 */
export interface UserTenantProxyService {
  /**
   * Finds users in a tenant using a filter.
   *
   * @param token - Authorization token.
   * @param id - Tenant ID.
   * @param filter - Filter object or string for querying users.
   * @returns A promise resolving to an array of User objects.
   */
  find(
    token: string,
    id: string,
    filter?: Filter<UserView> | string,
  ): Promise<UserView[]>;

  /**
   * Get roles by tenant user group
   * @param token
   */
  getRoleView(
    token: string,
    tenantId: string,
    filter?: string,
  ): Promise<Array<RoleView>>;

  /**
   * Get roles by tenant user group count
   * @param token
   * @param tenantId
   * @param where
   */
  getRoleViewCount(
    token: string,
    tenantId: string,
    where?: string,
  ): Promise<Count>;

  /**
   * Fetches a list of users from the User Tenant Service.
   * @param token - The authentication token for the request.
   * @param id - The tenant or user identifier.
   * @param filter - Optional filter criteria for querying users.
   * @returns A promise resolving to an array of UserView objects.
   */
  getUsersList(
    token: string,
    id: string,
    filter?: Filter<UserView> | string,
  ): Promise<UserView[]>;

  /**
   * Gets the count of users for a given tenant.
   * @param token - The authentication token for the request.
   * @param tenantId - The tenant identifier.
   * @param where - Optional filter criteria as a string.
   * @returns A promise resolving to a Count object.
   */
  getUserCount(token: string, tenantId: string, where?: string): Promise<Count>;

  /**
   * Updates an existing user.
   *
   * @param token - Authorization token.
   * @param tenantId - The tenant identifier.
   * @param userId - User ID to update.
   * @param body - DTO containing updated user details.
   * @returns A promise resolving to the updated User object.
   */
  updateUser(
    token: string,
    tenantId: string,
    userId: string,
    body: Partial<Omit<CreateUserOnboardDto, 'email'>>,
  ): Promise<User>;

  /**
   * Bulk user creation.
   * @param token - Authorization token.
   * @param tenantId - The tenant identifier.
   * @param body - Array of CreateUserOnboardDto objects.
   * @returns A promise resolving to an array of created users or error with duplicate emails.
   */
  createBulkUsers(
    token: string,
    tenantId: string,
    body: CreateUserOnboardDto[],
  ): Promise<User[]>;

  /**
   * Updates the status of a user in a tenant.
   * @param token - Authorization token.
   * @param tenantId - The tenant identifier.
   * @param userId - User ID.
   * @param body - DTO containing the new status.
   */
  updateUserStatus(
    token: string,
    tenantId: string,
    userId: string,
    body: UpdateUserStatusRequestDto,
  ): Promise<User>;

  /**
   * Finds roles for a user.
   * @param token - Authorization token.
   * @param id - User ID.
   * @param filter - Filter object or string for querying roles.
   */
  getRoles(
    token: string,
    id: string,
    filter: Filter<Role> | string,
  ): Promise<Role[]>;
}

/**
 * Provider class for the UserTenantProxyService.
 *
 * Uses the UserTenantServiceDataSource to create a proxy client for communicating with the user-tenant service.
 */
export class UserTenantProxyServiceProvider
  implements Provider<UserTenantProxyService>
{
  /**
   * Constructs a new UserTenantProxyServiceProvider.
   *
   * @param dataSource - The datasource connected to the user-tenant service.
   */
  constructor(
    @inject('datasources.UserTenantService')
    protected dataSource: UserTenantServiceDataSource,
  ) {}

  /**
   * Returns the service proxy for UserTenantProxyService.
   *
   * @returns A promise resolving to an implementation of UserTenantProxyService.
   */
  value(): Promise<UserTenantProxyService> {
    return getService(this.dataSource);
  }
}
