import {BindingScope, inject, injectable} from '@loopback/context';
import {Filter} from '@loopback/repository';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {Role} from '@sourceloop/user-tenant-service';
import {UserTenantProxyService} from './proxies';

/**
 * Service to assist with role-related operations, such as fetching roles
 * while handling authorization and filtering out super admin roles.
 */
@injectable({scope: BindingScope.TRANSIENT})
export class RoleHelperService {
  /**
   * The authorization token extracted from the request headers.
   */
  token: string;

  /**
   * Constructs a new RoleHelperService.
   * Extracts the authorization token from the request headers and injects the UserProxyService.
   * @param request - The current HTTP request object.
   * @param userProxyService - The proxy service for user operations.
   * @throws {HttpErrors.Unauthorized} If the authorization header is missing.
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.UserTenantProxyService')
    private readonly userProxyService: UserTenantProxyService,
  ) {
    // Extract the Authorization header from the request
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    } else {
      throw new HttpErrors.Unauthorized();
    }
  }

  /**
   * Retrieves a list of roles, excluding the super admin role.
   * Throws InternalServerError if DEFAULT_TENANT_ID is not set.
   * @param filter - Optional filter criteria for querying roles.
   * @returns A promise resolving to an array of Role objects.
   * @throws {HttpErrors.InternalServerError} If DEFAULT_TENANT_ID is not set.
   */
  async getRoles(filter?: Filter<Role>): Promise<Role[]> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        'DEFAULT_TENANT_ID is not defined in environment variables',
      );
    }
    // Exclude the super admin role from the results
    const filterWhere = filter?.where ?? {};

    const refinedFilter: Filter<Role> = {
      ...filter,
      where: {
        ...filterWhere,
        name: {neq: process.env.SUPER_ADMIN_ROLE_NAME!},
      },
    };
    // Extract the Bearer token value (without "Bearer " prefix)
    const accessToken = this.token?.split(' ')[1];
    // Delegate the role retrieval to the userProxyService
    return this.userProxyService.getRoles(
      accessToken,
      process.env.DEFAULT_TENANT_ID,
      refinedFilter,
    );
  }
}
