// Unit tests for RoleHelperService
import {expect, sinon} from '@loopback/testlab';
import {RestBindings, HttpErrors, Request} from '@loopback/rest';
import {Context} from '@loopback/context';
import {Role} from '@sourceloop/user-tenant-service';
import {RoleHelperService} from '../../../services/role-helper.service';

describe('RoleHelperService', () => {
  const userProxyServiceStub = {
    getRoles: sinon.stub(),
    find: sinon.stub(),
    getRoleView: sinon.stub(),
    getRoleViewCount: sinon.stub(),
    getUsersList: sinon.stub(),
    createBulkUsers: sinon.stub(),
    updateUser: sinon.stub(),
    updateUserStatus: sinon.stub(),
    getUserCount: sinon.stub(),
  };
  let request: Request;
  let ctx: Context;

  beforeEach(() => {
    Object.values(userProxyServiceStub).forEach(
      stub => stub.reset && stub.reset(),
    );
    ctx = new Context();
    ctx.bind('services.UserTenantProxyService').to(userProxyServiceStub);
  });

  it('throws Unauthorized if authorization header missing', () => {
    request = {headers: {}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);
    expect(() => {
      new RoleHelperService(request, userProxyServiceStub);
    }).to.throw(HttpErrors.Unauthorized);
  });

  it('getRoles throws InternalServerError if DEFAULT_TENANT_ID missing', async () => {
    request = {headers: {authorization: 'Bearer token123'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const service = new RoleHelperService(request, userProxyServiceStub);
    delete process.env.DEFAULT_TENANT_ID;
    await expect(service.getRoles()).to.be.rejectedWith(
      HttpErrors.InternalServerError,
    );
  });

  it('getRoles returns roles excluding super admin', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant1';
    process.env.SUPER_ADMIN_ROLE_NAME = 'superadmin';
    request = {headers: {authorization: 'Bearer token123'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const roles = [
      new Role({id: '1', name: 'user'}),
      new Role({id: '2', name: 'admin'}),
    ];
    userProxyServiceStub.getRoles.resolves(roles);

    const service = new RoleHelperService(request, userProxyServiceStub);
    const result = await service.getRoles();
    expect(result).to.eql(roles);
    sinon.assert.calledOnce(userProxyServiceStub.getRoles);
    const [token, tenantId, filter] =
      userProxyServiceStub.getRoles.getCall(0).args;
    expect(token).to.equal('token123');
    expect(tenantId).to.equal('tenant1');
    expect(filter.where.name).to.deepEqual({neq: 'superadmin'});
  });

  it('getRoles applies additional filter', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant2';
    process.env.SUPER_ADMIN_ROLE_NAME = 'superadmin';
    request = {headers: {authorization: 'Bearer token456'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const roles = [new Role({id: '3', name: 'manager'})];
    userProxyServiceStub.getRoles.resolves(roles);

    const service = new RoleHelperService(request, userProxyServiceStub);
    const filter = {where: {custom: 'value'}} as {
      where: {[key: string]: unknown};
    };
    const result = await service.getRoles(filter);
    expect(result).to.eql(roles);
    sinon.assert.calledOnce(userProxyServiceStub.getRoles);
    const [token, tenantId, calledFilter] =
      userProxyServiceStub.getRoles.getCall(0).args;
    expect(token).to.equal('token456');
    expect(tenantId).to.equal('tenant2');
    expect(calledFilter.where.name).to.deepEqual({neq: 'superadmin'});
    expect(calledFilter.where.custom).to.equal('value');
  });
});
