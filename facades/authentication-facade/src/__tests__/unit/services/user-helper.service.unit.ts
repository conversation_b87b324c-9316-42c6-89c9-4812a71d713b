// Unit tests for UserHelperService
import {expect, sinon} from '@loopback/testlab';
import {RestBindings, HttpErrors, Request} from '@loopback/rest';
import {Context} from '@loopback/context';
import {User, UserView} from '@sourceloop/user-tenant-service';
import {UserHelperService} from '../../../services';
import {UserStatus} from '@sourceloop/core';
import {CreateUserRequestDto} from '../../../models/dto/create-user-req.dto.model';
import {UpdateUserStatusRequestDto} from '../../../models/dto/update-user-status-req.dto.model';

describe('UserHelperService', () => {
  // Dependency stubs created at the top and reused in all tests
  const userProxyServiceStub = {
    getUserCount: sinon.stub(),
    getUsersList: sinon.stub(),
    createBulkUsers: sinon.stub(),
    updateUser: sinon.stub(),
    updateUserStatus: sinon.stub(),
    getRoles: sinon.stub(),
    // Add missing methods for type compatibility
    find: sinon.stub(),
    getRoleView: sinon.stub(),
    getRoleViewCount: sinon.stub(),
  };
  const cryptoHelperServiceStub = {
    generateTempToken: sinon.stub().returns('mocked-token'),
    generateRandomString: sinon.stub(),
  };
  const authenticatorServiceStub = {
    setForgetPasswordTempToken: sinon.stub().resolves('reset-token'),
    userTokenRepository: {},
    _generateTempToken: sinon.stub(),
  } as unknown as import('../../../../src/services/authenticator.service').AuthenticatorService;
  const notificationProxyServiceStub = {
    createNotification: sinon.stub().resolves({}),
    getTemplateByName: sinon.stub().resolves({
      subject: 'Test Subject',
      body: 'Test Body',
    }),
    createBulkNotification: sinon.stub().resolves([]),
  };
  const templateServiceStub = {
    generateEmail: sinon.stub().returns('Email content with link'),
  };
  const currentUserStub = {
    id: 'user-id',
    userTenantId: 'tenant-user-id',
    tenantId: 'tenant-id',
    permissions: [],
    authClientId: 123,
    role: 'user',
    firstName: 'Test',
    lastName: 'User',
    username: 'testuser',
  };
  // ILogger stub with required 'log' method for type compatibility
  const loggerStub = {
    error: sinon.stub(),
    info: sinon.stub(),
    debug: sinon.stub(),
    warn: sinon.stub(),
    log: sinon.stub(),
  } as unknown as import('@sourceloop/core').ILogger;
  let request: Request;
  let ctx: Context;

  beforeEach(() => {
    // Reset all sinon stubs before each test
    Object.values(userProxyServiceStub).forEach(
      stub => stub.reset && stub.reset(),
    );
    Object.values(cryptoHelperServiceStub).forEach(
      stub => stub.reset && stub.reset(),
    );
    Object.values(authenticatorServiceStub).forEach(stub => stub.reset?.());
    Object.values(notificationProxyServiceStub).forEach(
      stub => stub.reset && stub.reset(),
    );
    Object.values(templateServiceStub).forEach(
      stub => stub.reset && stub.reset(),
    );
    Object.values(loggerStub).forEach(stub => stub.reset?.());
    ctx = new Context();
    ctx.bind('services.UserProxyService').to(userProxyServiceStub);
  });

  it('throws Unauthorized if authorization header missing', () => {
    request = {headers: {}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);
    expect(() => {
      new UserHelperService(
        request,
        userProxyServiceStub,
        cryptoHelperServiceStub,
        authenticatorServiceStub,
        notificationProxyServiceStub,
        templateServiceStub,
        currentUserStub,
        loggerStub,
      );
    }).to.throw(HttpErrors.Unauthorized);
  });

  it('calls getUsersCount and returns result', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant1';
    process.env.SUPER_ADMIN_ROLE_NAME = 'superadmin';
    request = {headers: {authorization: 'Bearer token123'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    userProxyServiceStub.getUserCount.resolves({count: 7});

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    const result = await service.getUsersCount({firstName: 'John'});
    expect(result).to.deepEqual({count: 7});
    sinon.assert.calledOnce(userProxyServiceStub.getUserCount);
    const [token, tenantId, where] =
      userProxyServiceStub.getUserCount.getCall(0).args;
    expect(token).to.equal('Bearer token123');
    expect(tenantId).to.equal('tenant1');
    if (typeof where === 'string') {
      const parsed = JSON.parse(where);
      expect(parsed).to.have.property('and');
      expect(parsed.and[1]).to.deepEqual({roleName: {neq: 'superadmin'}});
    }
  });

  it('getUsersCount throws InternalServerError if DEFAULT_TENANT_ID missing', async () => {
    delete process.env.DEFAULT_TENANT_ID;
    request = {headers: {authorization: 'Bearer token123'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    await expect(service.getUsersCount()).to.be.rejectedWith(
      HttpErrors.InternalServerError,
    );
  });

  it('calls getUserLists and returns result', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant2';
    process.env.SUPER_ADMIN_ROLE_NAME = 'superadmin';
    request = {headers: {authorization: 'Bearer token456'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const userView = new UserView({
      id: '1',
      firstName: 'A',
      lastName: 'X',
      username: 'ax',
      email: '<EMAIL>',
      authClientIds: '',
      roleName: 'user',
      status: undefined,
      createdOn: new Date(),
      createdBy: 'system',
      phone: '',
      tenantId: 'tenant2',
      defaultTenantId: 'tenant2',
      roleId: 'role1',
      tenantName: 'Tenant X',
      userTenantId: 'utid1',
      lastLogin: undefined,
    });

    const userView2 = new UserView({
      id: '2',
      firstName: 'B',
      lastName: 'Y',
      username: 'by',
      email: '<EMAIL>',
      authClientIds: '',
      roleName: 'user',
      status: undefined,
      createdOn: new Date(),
      createdBy: 'system',
      phone: '',
      tenantId: 'tenant2',
      defaultTenantId: 'tenant2',
      roleId: 'role2',
      tenantName: 'Tenant Y',
      userTenantId: 'utid2',
      lastLogin: undefined,
    });

    userProxyServiceStub.getUsersList.resolves([userView, userView2]);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    const result = await service.getUserLists({where: {firstName: 'A'}});
    expect(result).to.have.length(2);
    sinon.assert.calledOnce(userProxyServiceStub.getUsersList);
    const [token, tenantId, filter] =
      userProxyServiceStub.getUsersList.getCall(0).args;
    expect(token).to.equal('token456');
    expect(tenantId).to.equal('tenant2');
    if (
      filter &&
      typeof filter !== 'string' &&
      filter.where &&
      Array.isArray((filter.where as {and?: unknown[]}).and)
    ) {
      const andArr = (filter.where as {and?: unknown[]}).and as unknown[];
      expect(andArr[1]).to.deepEqual({roleName: {neq: 'superadmin'}});
    }
  });

  it('getUserLists throws InternalServerError if DEFAULT_TENANT_ID missing', async () => {
    delete process.env.DEFAULT_TENANT_ID;
    request = {headers: {authorization: 'Bearer token456'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    await expect(service.getUserLists()).to.be.rejectedWith(
      HttpErrors.InternalServerError,
    );
  });

  it('getUserLists extracts access token part from authorization header', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant3';
    process.env.SUPER_ADMIN_ROLE_NAME = 'superadmin';
    request = {headers: {authorization: 'Bearer token789'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    userProxyServiceStub.getUsersList.resolves([]);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    await service.getUserLists();
    const [token] = userProxyServiceStub.getUsersList.getCall(0).args;
    expect(token).to.equal('token789');
  });

  // New tests for uncovered methods

  it('getUserById returns user if found', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant4';
    request = {headers: {authorization: 'Bearer token999'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const userView = new UserView({id: 'u1', roleName: 'user'});
    userProxyServiceStub.getUsersList.resolves([userView]);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    const result = await service.getUserById('u1');
    expect(result).to.eql(userView);
  });

  it('getUserById throws NotFound if user not found', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant4';
    request = {headers: {authorization: 'Bearer token999'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    userProxyServiceStub.getUsersList.resolves([]);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    await expect(service.getUserById('u1')).to.be.rejectedWith(
      HttpErrors.NotFound,
    );
  });

  it('updateUser updates user and returns updated user', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant5';
    request = {headers: {authorization: 'Bearer tokenUpdate'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const userView = new UserView({id: 'u2', roleName: 'user'});
    userProxyServiceStub.getUsersList.onFirstCall().resolves([userView]);
    userProxyServiceStub.updateUser.resolves();
    userProxyServiceStub.getUsersList.onSecondCall().resolves([userView]);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    const dto: Partial<Omit<CreateUserRequestDto, 'email'>> = {
      fullName: 'Test User',
      roleId: 'roleX',
    };
    const result = await service.updateUser('u2', dto);
    expect(result).to.eql(userView);
    sinon.assert.calledOnce(userProxyServiceStub.updateUser);
  });

  it('updateUser throws NotFound if user not found', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant5';
    request = {headers: {authorization: 'Bearer tokenUpdate'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    userProxyServiceStub.getUsersList.resolves([]);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    const dto: Partial<Omit<CreateUserRequestDto, 'email'>> = {
      fullName: 'Test User',
      roleId: 'roleX',
    };
    await expect(service.updateUser('u2', dto)).to.be.rejectedWith(
      HttpErrors.NotFound,
    );
  });

  // --- sendNotification unit tests ---

  describe('UserHelperService sendNotification', () => {
    function makeUserView(id: string, email: string): UserView {
      return new UserView({
        id,
        email,
        firstName: 'First',
        lastName: 'Last',
        username: email.split('@')[0],
        authClientIds: '',
        roleName: 'user',
        status: undefined,
        createdOn: new Date(),
        createdBy: 'system',
        phone: '',
        tenantId: 'tenant-notif',
        defaultTenantId: 'tenant-notif',
        roleId: 'role1',
        tenantName: 'Tenant',
        userTenantId: 'utid-' + id,
        lastLogin: undefined,
      });
    }

    function makeUser(id: string, email: string): User {
      // Use empty object for credentials to avoid type error
      return new User({
        id,
        email,
        firstName: 'First',
        lastName: 'Last',
        username: email.split('@')[0],
        credentials: {
          authProvider: '',
          userId: id,
          getId: () => id,
          getIdObject: () => ({id}),
          toJSON: () => ({}),
          toObject: () => ({}),
        },
        createdOn: new Date(),
        createdBy: 'system',
        phone: '',
        tenantId: 'tenant-notif',
        defaultTenantId: 'tenant-notif',
        roleId: 'role1',
        tenantName: 'Tenant',
        userTenantId: 'utid-' + id,
        lastLogin: undefined,
        status: undefined,
        authClientIds: '',
        roleName: 'user',
      });
    }

    beforeEach(() => {
      process.env.DEFAULT_TENANT_ID = 'tenant-notif';
      process.env.CLIENT_ID = 'client-id';
      process.env.SET_PASSWORD_LINK = 'https://set-link.example.com';
      process.env.NOTIFICATION_FROM_EMAIL = '<EMAIL>';
    });

    it('sends notifications to all users', async () => {
      request = {headers: {authorization: 'Bearer tokenNotif'}} as Request;
      ctx.bind(RestBindings.Http.REQUEST).to(request);

      const users = [
        makeUser('u1', '<EMAIL>'),
        makeUser('u2', '<EMAIL>'),
      ];

      // Mock getUserById to return userView for each user
      sinon
        .stub(UserHelperService.prototype, 'getUserById')
        .onFirstCall()
        .resolves(makeUserView('u1', '<EMAIL>'))
        .onSecondCall()
        .resolves(makeUserView('u2', '<EMAIL>'));

      (authenticatorServiceStub.setForgetPasswordTempToken as sinon.SinonStub)
        .onFirstCall()
        .resolves('token1')
        .onSecondCall()
        .resolves('token2');

      (
        notificationProxyServiceStub.getTemplateByName as sinon.SinonStub
      ).resolves({subject: 'Subject', body: 'Body'});

      (templateServiceStub.generateEmail as sinon.SinonStub).returns(
        'Email content',
      );

      (
        notificationProxyServiceStub.createNotification as sinon.SinonStub
      ).resolves({});

      const service = new UserHelperService(
        request,
        userProxyServiceStub,
        cryptoHelperServiceStub,
        authenticatorServiceStub,
        notificationProxyServiceStub,
        templateServiceStub,
        currentUserStub,
        loggerStub,
      );

      await service.sendNotification(users);

      sinon.assert.calledTwice(notificationProxyServiceStub.createNotification);
      sinon.assert.calledTwice(templateServiceStub.generateEmail);
      // Remove incorrect assertion on stub, just check call count directly
      expect(
        (authenticatorServiceStub.setForgetPasswordTempToken as sinon.SinonStub)
          .callCount,
      ).to.equal(2);
      sinon.assert.calledTwice(
        UserHelperService.prototype.getUserById as sinon.SinonStub,
      );

      (UserHelperService.prototype.getUserById as sinon.SinonStub).restore();
    });

    it('throws InternalServerError if template is not found', async () => {
      request = {headers: {authorization: 'Bearer tokenNotif'}} as Request;
      ctx.bind(RestBindings.Http.REQUEST).to(request);

      const users = [makeUser('u1', '<EMAIL>')];

      sinon
        .stub(UserHelperService.prototype, 'getUserById')
        .resolves(makeUserView('u1', '<EMAIL>'));
      (
        notificationProxyServiceStub.getTemplateByName as sinon.SinonStub
      ).resolves(undefined);

      const service = new UserHelperService(
        request,
        userProxyServiceStub,
        cryptoHelperServiceStub,
        authenticatorServiceStub,
        notificationProxyServiceStub,
        templateServiceStub,
        currentUserStub,
        loggerStub,
      );

      await expect(service.sendNotification(users)).to.be.rejectedWith(
        HttpErrors.InternalServerError,
      );

      (UserHelperService.prototype.getUserById as sinon.SinonStub).restore();
    });
  });

  it('createBulkUsers throws Conflict if duplicate emails found', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant6';
    request = {headers: {authorization: 'Bearer tokenBulk'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    const dtos = [
      new CreateUserRequestDto({
        email: '<EMAIL>',
        fullName: 'A',
        roleId: 'role1',
      }),
      new CreateUserRequestDto({
        email: '<EMAIL>',
        fullName: 'B',
        roleId: 'role2',
      }),
    ];
    await expect(service.createBulkUsers(dtos)).to.be.rejectedWith(
      HttpErrors.Conflict,
    );
  });

  it('createBulkUsers calls proxy and sends notification', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant6';
    request = {headers: {authorization: 'Bearer tokenBulk'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const users = [{id: 'u1', email: '<EMAIL>'}];
    userProxyServiceStub.createBulkUsers.resolves(users);
    sinon.stub(UserHelperService.prototype, 'sendNotification').resolves();

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    const dtos = [
      new CreateUserRequestDto({
        email: '<EMAIL>',
        fullName: 'A',
        roleId: 'role1',
      }),
    ];
    const result = await service.createBulkUsers(dtos);
    expect(result).to.eql(users);
    sinon.assert.calledOnce(userProxyServiceStub.createBulkUsers);
    (UserHelperService.prototype.sendNotification as sinon.SinonStub).restore();
  });

  it('sendNotification throws if DEFAULT_TENANT_ID missing', async () => {
    delete process.env.DEFAULT_TENANT_ID;
    request = {headers: {authorization: 'Bearer tokenNotif'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    // Provide a minimal valid User object for negative test
    const invalidUser = new User({
      id: 'u1',
      email: '<EMAIL>',
      firstName: '',
      lastName: '',
      username: '',
      credentials: {
        authProvider: '',
        userId: 'u1',
        getId: () => 'u1',
        getIdObject: () => ({}),
        toJSON: () => ({}),
        toObject: () => ({}),
      },
    });
    await expect(service.sendNotification([invalidUser])).to.be.rejectedWith(
      HttpErrors.InternalServerError,
    );
  });

  it('updateUserStatus throws if DEFAULT_TENANT_ID missing', async () => {
    delete process.env.DEFAULT_TENANT_ID;
    request = {headers: {authorization: 'Bearer tokenStatus'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    await expect(
      service.updateUserStatus(
        'u1',
        new UpdateUserStatusRequestDto({status: UserStatus.ACTIVE}),
      ),
    ).to.be.rejectedWith(HttpErrors.InternalServerError);
  });

  it('updateUserStatus throws NotFound if user not found', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant7';
    request = {headers: {authorization: 'Bearer tokenStatus'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    userProxyServiceStub.getUsersList.resolves([]);

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    await expect(
      service.updateUserStatus(
        'u1',
        new UpdateUserStatusRequestDto({status: UserStatus.ACTIVE}),
      ),
    ).to.be.rejectedWith(HttpErrors.NotFound);
  });

  it('updateUserStatus updates user and sends notification if status changes from INACTIVE to ACTIVE', async () => {
    process.env.DEFAULT_TENANT_ID = 'tenant7';
    request = {headers: {authorization: 'Bearer tokenStatus'}} as Request;
    ctx.bind(RestBindings.Http.REQUEST).to(request);

    const userView = {id: 'u1', status: 'INACTIVE', roleName: 'user'};
    userProxyServiceStub.getUsersList.resolves([userView]);
    userProxyServiceStub.updateUserStatus.resolves();
    sinon.stub(UserHelperService.prototype, 'sendNotification').resolves();

    const service = new UserHelperService(
      request,
      userProxyServiceStub,
      cryptoHelperServiceStub,
      authenticatorServiceStub,
      notificationProxyServiceStub,
      templateServiceStub,
      currentUserStub,
      loggerStub,
    );
    await service.updateUserStatus(
      'u1',
      new UpdateUserStatusRequestDto({status: UserStatus.ACTIVE}),
    );
    sinon.assert.calledOnce(userProxyServiceStub.updateUserStatus);
    (UserHelperService.prototype.sendNotification as sinon.SinonStub).restore();
  });
});
