// Copyright (c) 2025 Sourcefuse Technologies

import {Client, expect, sinon} from '@loopback/testlab';
import {setupApplication, getToken} from './test-helper';
import {AuthenticationFacadeApplication} from '../../application';
import {RoleHelperService} from '../../services';
import {Role} from '@sourceloop/authentication-service';
import {PermissionKey} from '@local/core';

describe('RoleController (acceptance)', () => {
  let app: AuthenticationFacadeApplication;
  let client: Client;
  let roleHelperStub: Partial<RoleHelperService>;

  const role: Role = new Role({
    id: 'role-1',
    name: 'Admin',
  });

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());

    roleHelperStub = {
      getRoles: sinon.stub().resolves([role]),
    };

    app.bind('services.RoleHelperService').to(roleHelperStub);
  });

  after(async () => {
    await app.stop();
  });

  afterEach(() => {
    sinon.restore();
  });

  const authHeader = () => ({
    Authorization: getToken([PermissionKey.ViewRoles]),
  });

  it('GET /roles returns roles with valid auth', async () => {
    const res = await client.get('/roles').set(authHeader()).expect(200);
    expect(res.body).to.be.Array();
    expect(res.body[0]).to.containEql({id: 'role-1', name: 'Admin'});
    sinon.assert.calledOnce(roleHelperStub.getRoles as sinon.SinonStub);
  });

  it('GET /roles supports filter query', async () => {
    const filter = {where: {name: 'Admin'}};
    await client
      .get('/roles')
      .set(authHeader())
      .query({filter: JSON.stringify(filter)})
      .expect(200);
    sinon.assert.calledWith(
      roleHelperStub.getRoles as sinon.SinonStub,
      sinon.match(filter),
    );
  });

  it('GET /roles returns 401 without auth', async () => {
    await client.get('/roles').expect(401);
  });

  it('GET /roles returns 403 without permission', async () => {
    const token = getToken([]); // No permissions
    await client.get('/roles').set({Authorization: token}).expect(403);
  });

  it('GET /roles propagates errors from helper service', async () => {
    (roleHelperStub.getRoles as sinon.SinonStub).rejects({
      statusCode: 500,
      message: 'Internal error',
    });
    await client.get('/roles').set(authHeader()).expect(500);
  });
});
