{"openapi": "3.0.0", "info": {"title": "tenant-mgmt-facade", "version": "1.0.0", "description": "tenant-mgmt-facade", "contact": {}}, "paths": {"/configure-devices": {"get": {"x-controller-name": "ConfigureDeviceControllerController", "x-operation-name": "find", "tags": ["ConfigureDeviceControllerController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of ConfigureDevice model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConfigureDeviceWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6024   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/configure_devices.Filter"}}}}], "operationId": "ConfigureDeviceControllerController.find"}}, "/currencies": {"get": {"x-controller-name": "C<PERSON><PERSON>cyController", "x-operation-name": "find", "tags": ["C<PERSON><PERSON>cyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Currency model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7028   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/currencies.Filter"}}}}], "operationId": "CurrencyController.find"}}, "/invoices/count": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "count", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "invoice.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Invoice>"}}}}], "operationId": "InvoiceController.count"}}, "/invoices/{id}/pdf-url": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "getStripeInvoicePdfUrl", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Stripe Invoice PDF URL", "content": {"application/json": {"schema": {"type": "object", "properties": {"pdfUrl": {"type": "string", "nullable": true}}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| DownloadInvoice   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "stripeInvoiceId", "in": "query", "schema": {"type": "string"}}], "operationId": "InvoiceController.getStripeInvoicePdfUrl"}}, "/invoices/{id}": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "findById", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Invoice model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/invoice.Filter"}}}}], "operationId": "InvoiceController.findById"}}, "/invoices": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "find", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of ConfigureDevice model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/invoice.Filter1"}}}}], "operationId": "InvoiceController.find"}}, "/ping": {"get": {"x-controller-name": "PingController", "x-operation-name": "ping", "tags": ["PingController"], "responses": {"200": {"description": "Ping Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PingResponse"}}}}}, "description": "", "operationId": "PingController.ping"}}, "/plan-history": {"get": {"x-controller-name": "PlanHistoryController", "x-operation-name": "find", "tags": ["PlanHistoryController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of PlanHistory model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanHistoryWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6020   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/plan_history.Filter"}}}}], "operationId": "PlanHistoryController.find"}}, "/plan-storage": {"get": {"x-controller-name": "PlanStorageController", "x-operation-name": "find", "tags": ["PlanStorageController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of PlanSizes model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanSizesWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7029   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/plan_sizes.Filter"}}}}], "operationId": "PlanStorageController.find"}}, "/plans/all-status": {"get": {"x-controller-name": "PlansController", "x-operation-name": "findAllStatus", "tags": ["PlansController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Object of all possible tenant status", "permissions": ["10216"], "content": {"application/json": {"schema": {"type": "object", "items": {}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10216   |\n", "operationId": "PlansController.findAllStatus"}}, "/plans/count": {"get": {"x-controller-name": "PlansController", "x-operation-name": "count", "tags": ["PlansController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7008   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "plans.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Plan>"}}}}], "operationId": "PlansController.count"}}, "/plans/{id}/status": {"patch": {"x-controller-name": "PlansController", "x-operation-name": "updatePlanStatusById", "tags": ["PlansController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Plan PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7010   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanStatusDtoPartial"}}}, "x-parameter-index": 1}, "operationId": "PlansController.updatePlanStatusById"}}, "/plans/{id}": {"patch": {"x-controller-name": "PlansController", "x-operation-name": "updatePlanById", "tags": ["PlansController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Plan PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7010   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditPlanDtoPartial"}}}, "x-parameter-index": 1}, "operationId": "PlansController.updatePlanById"}, "get": {"x-controller-name": "PlansController", "x-operation-name": "findById", "tags": ["PlansController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7008   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/plans.Filter"}}}}], "operationId": "PlansController.findById"}}, "/plans": {"post": {"x-controller-name": "PlansController", "x-operation-name": "create", "tags": ["PlansController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Plan"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7009   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewPlan"}}}}, "operationId": "PlansController.create"}, "get": {"x-controller-name": "PlansController", "x-operation-name": "find", "tags": ["PlansController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Plan model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7008   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/plans.Filter1"}}}}], "operationId": "PlansController.find"}}, "/subscriptions": {"get": {"x-controller-name": "SubscriptionController", "x-operation-name": "find", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Subscription model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7024   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/subscriptions.Filter"}}}}], "operationId": "SubscriptionController.find"}}, "/subscriptions-tenure": {"get": {"x-controller-name": "SubscriptionTenureController", "x-operation-name": "find", "tags": ["SubscriptionTenureController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of BillingCycle model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BillingCycleWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7024   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/billing_cycles.Filter"}}}}], "operationId": "SubscriptionTenureController.find"}}, "/tenant-billings/all-status": {"get": {"x-controller-name": "TenantBillingsController", "x-operation-name": "findAllStatus", "tags": ["TenantBillingsController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Object of all possible billing status", "permissions": ["10216"], "content": {"application/json": {"schema": {"type": "object", "items": {}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10216   |\n", "operationId": "TenantBillingsController.findAllStatus"}}, "/tenant-billings/count": {"get": {"x-controller-name": "TenantBillingsController", "x-operation-name": "count", "tags": ["TenantBillingsController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "tenant_billings_view.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<TenantBillingsView>"}}}}], "operationId": "TenantBillingsController.count"}}, "/tenant-billings": {"get": {"x-controller-name": "TenantBillingsController", "x-operation-name": "find", "tags": ["TenantBillingsController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of ConfigureDevice model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TenantBillingsViewWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/tenant_billings_view.Filter"}}}}], "operationId": "TenantBillingsController.find"}}, "/tenants/all-statuses": {"get": {"x-controller-name": "TenantController", "x-operation-name": "getAllStatuses", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of tenant statuses", "content": {"application/json": {"schema": {"type": "array", "items": {}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10216   |\n", "operationId": "TenantController.getAllStatuses"}}, "/tenants/count": {"get": {"x-controller-name": "TenantController", "x-operation-name": "count", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenants Count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10207   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "tenants.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Tenant>"}}}}], "operationId": "TenantController.count"}}, "/tenants/verify-key": {"post": {"x-controller-name": "TenantController", "x-operation-name": "<PERSON><PERSON><PERSON>", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "couples of possible leads if not requested key is not available", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeySuggestionDto"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10207   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyKeyDto"}}}}, "operationId": "TenantController.verifyKey"}}, "/tenants/{id}/edit": {"patch": {"x-controller-name": "TenantController", "x-operation-name": "updateTenant", "tags": ["TenantController"], "description": "This api update a tenant with a contact info, address info, and plan and file info.\n\n| Permissions |\n| ------- |\n| 10205   |\n", "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"multipart/form-data": {"x-parser": "stream", "schema": {"type": "object", "properties": {"existingFiles": {"type": "object"}, "contact": {"$ref": "#/components/schemas/ContactExcluding_tenantId-id-email_"}, "numberOfUsers": {"type": "number", "description": "number of users for the tenant"}, "totalCost": {"type": "number", "description": "total cost of the tenant"}, "planId": {"type": "string"}, "files": {"type": "array", "items": {"type": "string", "format": "binary"}}, "city": {"type": "string", "description": "city of the tenant owner"}, "state": {"type": "string", "description": "state of the tenant owner"}}}}}, "description": "Request body for multipart/form-data based file upload", "required": true, "x-parameter-index": 1}, "operationId": "TenantController.updateTenant"}}, "/tenants/{id}": {"get": {"x-controller-name": "TenantController", "x-operation-name": "getTenantDetails", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant details with contact, files, address, and plan information", "content": {"application/json": {"schema": {"type": "object", "properties": {"tenantDetails": {"$ref": "#/components/schemas/TenantWithRelations"}, "contact": {"type": "object", "description": "Tenant admin contact details"}, "files": {"type": "array", "description": "Files uploaded for the tenant with pre-signed URLs", "items": {"type": "object", "properties": {"id": {"type": "string"}, "fileKey": {"type": "string"}, "originalName": {"type": "string"}, "source": {"type": "number"}, "size": {"type": "number"}, "signedUrl": {"type": "string", "description": "Pre-signed URL for file access"}, "tenantId": {"type": "string"}, "createdOn": {"type": "string"}, "modifiedOn": {"type": "string"}}}}, "address": {"type": "object", "description": "Address details of the tenant"}, "plan": {"type": "object", "description": "Plan details (currently empty)"}}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10207   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "TenantController.getTenantDetails"}}, "/tenants": {"post": {"x-controller-name": "TenantController", "x-operation-name": "onboard", "tags": ["TenantController"], "description": "This api creates a tenant with a contact, so it also expects contact info in the payload. The start of subscription is the time of creation of tenant and end date of plan depends on the duration of plan.\n\n| Permissions |\n| ------- |\n| 10204   |\n", "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Tenant model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tenant"}}}}}, "requestBody": {"content": {"multipart/form-data": {"x-parser": "stream", "schema": {"type": "object", "properties": {"contact": {"$ref": "#/components/schemas/ContactExcluding_tenantId-id_"}, "lang": {"type": "string", "enum": ["English"]}, "address": {"type": "string", "description": "address of the tenant owners"}, "numberOfUsers": {"type": "number", "description": "number of users for the tenant"}, "city": {"type": "string", "description": "city of the tenant owner"}, "state": {"type": "string", "description": "state of the tenant owner"}, "zip": {"type": "string", "description": "zip code of the tenant owner"}, "country": {"type": "string", "description": "country of the tenant owner"}, "totalCost": {"type": "number", "description": "total cost of the tenant"}, "name": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$", "errorMessage": {"pattern": "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen."}}, "planId": {"type": "string"}, "files": {"type": "array", "items": {"type": "string", "format": "binary"}}, "key": {"type": "string", "description": "A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.", "pattern": "^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$", "minLength": 3, "maxLength": 63}}}}}, "description": "Request body for multipart/form-data based file upload", "required": true}, "operationId": "TenantController.onboard"}, "get": {"x-controller-name": "TenantController", "x-operation-name": "getTenants", "tags": ["TenantController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Tenant model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TenantWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10207   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/tenants.Filter"}}}}], "operationId": "TenantController.getTenants"}}, "/webhook": {"post": {"x-controller-name": "WebhookController", "x-operation-name": "handleWebhook", "tags": ["WebhookController"], "description": "", "responses": {"200": {"description": "Return value of WebhookController.handleWebhook", "content": {}}}, "requestBody": {"content": {"application/json": {"x-parser": "raw", "schema": {"type": "object"}}}, "description": "Payment Provider webhook payload", "required": true}, "operationId": "WebhookController.handleWebhook"}}, "/": {"get": {"x-controller-name": "HomePageController", "x-operation-name": "homePage", "tags": ["HomePageController"], "responses": {"200": {"description": "Home Page", "content": {"text/html": {"schema": {"type": "string"}}}}}, "description": "", "operationId": "HomePageController.homePage"}}}, "components": {"securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"Tenant": {"title": "Tenant", "type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the tenant", "minLength": 3, "maxLength": 50, "pattern": "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$", "errorMessage": {"pattern": "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen."}}, "status": {"type": "number", "description": "status of a tenant, it can be - 0(active), 1(provisioning),2(deprovisioning),3(inactive)", "enum": [0, 1, 2, 3, 4, 5]}, "key": {"type": "string", "description": "A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.", "pattern": "^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$", "minLength": 3, "maxLength": 63}, "spocUserId": {"type": "string", "description": "user id of the admin user who acts as a spoc for this tenant."}, "domains": {"type": "array", "items": {"type": "string", "description": "array of domains that are allowed for this tenant"}}, "leadId": {"type": "string", "description": "id of the lead from which this tenant was generated. this is optional as a tenant can be created without this lead."}, "addressId": {"type": "string", "description": "id of the address of the tenant"}, "lang": {"type": "string"}, "planName": {"type": "string"}}, "required": ["name", "status", "key", "domains"], "additionalProperties": false}, "EditTenantDTO": {"title": "EditTenantDTO", "type": "object", "description": "model describing payload used to create and onboard a tenant", "properties": {"existingFiles": {"type": "object"}, "contact": {"type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: Omit<Contact, 'tenantId' | 'id' | 'email'>, schemaOptions: { exclude: [ 'tenantId', 'id', 'email' ] })", "title": "ContactExcluding_tenantId-id-email_", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "lastName": {"type": "string", "description": "last name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "designation": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "phoneNumber": {"type": "string", "minLength": 10, "maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Invalid phone number format. Use only digits.", "minLength": "Phone number must be at least 10 digits long.", "maxLength": "Phone number cannot exceed 10 characters."}}, "countryCode": {"type": "string", "minLength": 2, "maxLength": 5, "pattern": "^\\+[0-9]{1,4}$", "errorMessage": {"pattern": "Invalid country code format. It should start with an + followed by 1 to 4 digits.", "minLength": "Country code must be at least 2 characters.", "maxLength": "Country code cannot exceed 5 characters."}}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}}, "required": ["firstName", "lastName", "isPrimary"], "additionalProperties": false}, "numberOfUsers": {"type": "number", "description": "number of users for the tenant"}, "totalCost": {"type": "number", "description": "total cost of the tenant"}, "planId": {"type": "string"}, "files": {"type": "array", "items": {"type": "string"}}, "city": {"type": "string", "description": "city of the tenant owner"}, "state": {"type": "string", "description": "state of the tenant owner"}}, "required": ["existingFiles", "totalCost", "planId"], "additionalProperties": false}, "CreateTenantDTO": {"title": "CreateTenantDTO", "type": "object", "description": "model describing payload used to create and onboard a tenant", "properties": {"contact": {"type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: Omit<Contact, 'tenantId' | 'id'>, schemaOptions: { exclude: [ 'tenantId', 'id' ] })", "title": "ContactExcluding_tenantId-id_", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "lastName": {"type": "string", "description": "last name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "email": {"type": "string", "description": "email id of the contact", "format": "email", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,7}$", "errorMessage": {"pattern": "Invalid email format. Please provide a valid email address."}}, "designation": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "phoneNumber": {"type": "string", "minLength": 10, "maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Invalid phone number format. Use only digits.", "minLength": "Phone number must be at least 10 digits long.", "maxLength": "Phone number cannot exceed 10 characters."}}, "countryCode": {"type": "string", "minLength": 2, "maxLength": 5, "pattern": "^\\+[0-9]{1,4}$", "errorMessage": {"pattern": "Invalid country code format. It should start with an + followed by 1 to 4 digits.", "minLength": "Country code must be at least 2 characters.", "maxLength": "Country code cannot exceed 5 characters."}}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}}, "required": ["firstName", "lastName", "email", "isPrimary"], "additionalProperties": false}, "lang": {"type": "string", "enum": ["English"]}, "address": {"type": "string", "description": "address of the tenant owners"}, "numberOfUsers": {"type": "number", "description": "number of users for the tenant"}, "city": {"type": "string", "description": "city of the tenant owner"}, "state": {"type": "string", "description": "state of the tenant owner"}, "zip": {"type": "string", "description": "zip code of the tenant owner"}, "country": {"type": "string", "description": "country of the tenant owner"}, "totalCost": {"type": "number", "description": "total cost of the tenant"}, "name": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$", "errorMessage": {"pattern": "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen."}}, "planId": {"type": "string"}, "files": {"type": "array", "items": {"type": "string"}}, "key": {"type": "string", "description": "A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.", "pattern": "^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$", "minLength": 3, "maxLength": 63}}, "required": ["lang", "totalCost", "name", "planId", "key"], "additionalProperties": false}, "KeySuggestionDto": {"title": "KeySuggestionDto", "type": "object", "description": "model describing payload used to send resof verifying key for a tenant", "properties": {"available": {"type": "boolean"}, "suggestions": {"type": "array", "items": {"type": "string"}}}, "required": ["available", "suggestions"], "additionalProperties": false}, "VerifyKeyDto": {"title": "VerifyKeyDto", "type": "object", "description": "model describing payload used to verify key for a tenant", "properties": {"key": {"type": "string"}}, "required": ["key"], "additionalProperties": false}, "ContactWithRelations": {"title": "ContactWithRelations", "type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: ContactWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "lastName": {"type": "string", "description": "last name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "email": {"type": "string", "description": "email id of the contact", "format": "email", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,7}$", "errorMessage": {"pattern": "Invalid email format. Please provide a valid email address."}}, "designation": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "phoneNumber": {"type": "string", "minLength": 10, "maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Invalid phone number format. Use only digits.", "minLength": "Phone number must be at least 10 digits long.", "maxLength": "Phone number cannot exceed 10 characters."}}, "countryCode": {"type": "string", "minLength": 2, "maxLength": 5, "pattern": "^\\+[0-9]{1,4}$", "errorMessage": {"pattern": "Invalid country code format. It should start with an + followed by 1 to 4 digits.", "minLength": "Country code must be at least 2 characters.", "maxLength": "Country code cannot exceed 5 characters."}}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}, "tenantId": {"type": "string", "description": "tenant id this contact belongs to"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}}, "required": ["firstName", "lastName", "email", "isPrimary"], "additionalProperties": false}, "ResourceWithRelations": {"title": "ResourceWithRelations", "type": "object", "description": "model for resources that are provisioned for a tenant (tsType: ResourceWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "externalIdentifier": {"type": "string", "description": "identifier for the resource in the external system it was provisioned"}, "type": {"type": "string", "description": "type of the resource like storage, compute, etc"}, "metadata": {"type": "object", "description": "any type specific metadata of the resource like connection info, size, etc"}, "tenantId": {"type": "string", "description": "id of the tenant for which this resource is provisioned"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}}, "required": ["externalIdentifier", "type", "metadata"], "additionalProperties": false}, "LeadWithRelations": {"title": "LeadWithRelations", "type": "object", "description": "this model represents a lead that could eventually be a tenant in the system (tsType: LeadWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the lead"}, "lastName": {"type": "string", "description": "last name of the lead"}, "companyName": {"type": "string", "description": "name of the lead's company"}, "email": {"type": "string", "description": "email of the lead"}, "isValidated": {"type": "boolean", "description": "whether the lead`s email has been validated or not"}, "addressId": {"type": "string", "description": "id of the address of the lead"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "address": {"$ref": "#/components/schemas/AddressWithRelations"}, "foreignKey": {}}, "required": ["firstName", "lastName", "companyName", "email", "isValidated"], "additionalProperties": false}, "AddressWithRelations": {"title": "AddressWithRelations", "type": "object", "description": "this model represents the address of a company or lead (tsType: AddressWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "address": {"type": "string", "description": "address of the company"}, "city": {"type": "string", "description": "city of the company"}, "state": {"type": "string", "description": "state of the company"}, "zip": {"type": "string", "description": "zip code of the company"}, "country": {"type": "string", "description": "country of the company"}}, "required": ["country"], "additionalProperties": false}, "FileWithRelations": {"title": "FileWithRelations", "type": "object", "description": "(tsType: FileWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "tenantId": {"type": "string"}, "fileKey": {"type": "string"}, "originalName": {"type": "string"}, "source": {"type": "number"}, "size": {"type": "number"}, "tenant": {"$ref": "#/components/schemas/TenantWithRelations"}, "foreignKey": {}}, "additionalProperties": false, "x-typescript-type": "FileWithRelations"}, "TenantWithRelations": {"title": "TenantWithRelations", "type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: TenantWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the tenant", "minLength": 3, "maxLength": 50, "pattern": "^(?![-\\s])[a-zA-Z0-9\\s&.,'’-]+(?<![-\\s])$", "errorMessage": {"pattern": "Invalid name format. Use only letters, numbers, spaces, &, ., ,, ', and –. It cannot start or end with a space or hyphen."}}, "status": {"type": "number", "description": "status of a tenant, it can be - 0(active), 1(provisioning),2(deprovisioning),3(inactive)", "enum": [0, 1, 2, 3, 4, 5]}, "key": {"type": "string", "description": "A short string used to identify a tenant. This is also used as the namespace and subdomain for this particular tenant.", "pattern": "^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$", "minLength": 3, "maxLength": 63}, "spocUserId": {"type": "string", "description": "user id of the admin user who acts as a spoc for this tenant."}, "domains": {"type": "array", "items": {"type": "string", "description": "array of domains that are allowed for this tenant"}}, "leadId": {"type": "string", "description": "id of the lead from which this tenant was generated. this is optional as a tenant can be created without this lead."}, "addressId": {"type": "string", "description": "id of the address of the tenant"}, "lang": {"type": "string"}, "planName": {"type": "string"}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/ContactWithRelations"}}, "resources": {"type": "array", "items": {"$ref": "#/components/schemas/ResourceWithRelations"}}, "lead": {"$ref": "#/components/schemas/LeadWithRelations"}, "foreignKey": {}, "address": {"$ref": "#/components/schemas/AddressWithRelations"}, "files": {"type": "array", "items": {"$ref": "#/components/schemas/FileWithRelations"}}}, "required": ["name", "status", "key", "domains"], "additionalProperties": false}, "InvoiceWithRelations": {"title": "InvoiceWithRelations", "type": "object", "description": "(tsType: InvoiceWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "invoiceId": {"type": "string"}, "invoiceStatus": {"type": "string", "description": "payment or invoice status"}, "billingCustomerId": {"type": "string"}, "amount": {"type": "number"}, "tax": {"type": "number"}, "discount": {"type": "number"}, "dueDate": {"type": "string", "description": "due date for the invoice"}, "billingCustomer": {"$ref": "#/components/schemas/BillingCustomerWithRelations"}, "foreignKey": {}}, "required": ["invoiceId", "amount", "tax", "dueDate"], "additionalProperties": false, "x-typescript-type": "InvoiceWithRelations"}, "BillingCustomerWithRelations": {"title": "BillingCustomerWithRelations", "type": "object", "description": "contacts belonging to a tenant (tsType: BillingCustomerWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "tenantId": {"type": "string"}, "customerId": {"type": "string"}, "paymentSourceId": {"type": "string"}, "invoices": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceWithRelations"}}}, "required": ["tenantId", "customerId"], "additionalProperties": false}, "TenantBillingsViewWithRelations": {"title": "TenantBillingsViewWithRelations", "type": "object", "description": "(tsType: TenantBillingsViewWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "invoiceId": {"type": "string"}, "invoiceStatus": {"type": "string", "description": "payment or invoice status"}, "billingCustomerId": {"type": "string"}, "amount": {"type": "number"}, "tax": {"type": "number"}, "discount": {"type": "number"}, "dueDate": {"type": "string", "description": "due date for the invoice"}, "customerId": {"type": "string"}, "tenantId": {"type": "string"}, "tenantName": {"type": "string"}, "billingCustomer": {"$ref": "#/components/schemas/BillingCustomerWithRelations"}, "foreignKey": {}}, "required": ["invoiceId", "amount", "tax", "dueDate", "customerId", "tenantId", "tenantName"], "additionalProperties": false, "x-typescript-type": "TenantBillingsViewWithRelations"}, "BillingCycleWithRelations": {"title": "BillingCycleWithRelations", "type": "object", "description": "(tsType: BillingCycleWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "cycleName": {"type": "string"}, "duration": {"type": "number"}, "durationUnit": {"type": "string"}, "description": {"type": "string"}}, "required": ["cycleName", "duration", "durationUnit"], "additionalProperties": false, "x-typescript-type": "BillingCycleWithRelations"}, "CurrencyWithRelations": {"title": "CurrencyWithRelations", "type": "object", "description": "(tsType: C<PERSON>rencyWithRelations, schemaOptions: { includeRelations: true })", "properties": {"id": {"type": "string"}, "currencyCode": {"type": "string"}, "currencyName": {"type": "string"}, "symbol": {"type": "string"}, "country": {"type": "string"}}, "required": ["currencyCode", "currencyName"], "additionalProperties": false, "x-typescript-type": "CurrencyWithRelations"}, "ConfigureDeviceWithRelations": {"title": "ConfigureDeviceWithRelations", "type": "object", "description": "(tsType: ConfigureDeviceWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "min": {"type": "number"}, "max": {"type": "number"}, "computeSize": {"type": "string"}, "dbSize": {"type": "string"}}, "required": ["min", "max"], "additionalProperties": false, "x-typescript-type": "ConfigureDeviceWithRelations"}, "PlanSizesWithRelations": {"title": "PlanSizesWithRelations", "type": "object", "description": "(tsType: PlanSizesWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "size": {"type": "string"}, "config": {"type": "object"}}, "required": ["size"], "additionalProperties": false, "x-typescript-type": "PlanSizesWithRelations"}, "PlanHistoryWithRelations": {"title": "PlanHistoryWithRelations", "type": "object", "description": "(tsType: PlanHistoryWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "price": {"type": "number"}, "version": {"type": "string"}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "planId": {"type": "string"}, "plan": {"$ref": "#/components/schemas/PlanWithRelations"}, "foreignKey": {}}, "required": ["version"], "additionalProperties": false, "x-typescript-type": "PlanHistoryWithRelations"}, "PlanWithRelations": {"title": "PlanWithRelations", "type": "object", "description": "(tsType: PlanWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the plan"}, "description": {"type": "string", "description": "description of the plan"}, "tier": {"type": "string", "description": "Tier of the plan.", "enum": ["premium", "standard"]}, "price": {"type": "number"}, "metaData": {"type": "object", "description": "Meta data of the plan"}, "status": {"type": "number", "description": "Status of the plan.", "enum": [0, 1]}, "productRefId": {"type": "string", "description": "product reference ID of the plan."}, "version": {"type": "string", "description": "current version of the plan."}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "billingCycleId": {"type": "string"}, "currencyId": {"type": "string"}, "configureDeviceId": {"type": "string"}, "planSizeId": {"type": "string"}, "billingCycle": {"$ref": "#/components/schemas/BillingCycleWithRelations"}, "foreignKey": {}, "currency": {"$ref": "#/components/schemas/CurrencyWithRelations"}, "configureDevice": {"$ref": "#/components/schemas/ConfigureDeviceWithRelations"}, "planSize": {"$ref": "#/components/schemas/PlanSizesWithRelations"}, "planHistories": {"type": "array", "items": {"$ref": "#/components/schemas/PlanHistoryWithRelations"}}}, "required": ["name", "tier", "price", "status", "productRefId", "version"], "additionalProperties": false, "x-typescript-type": "PlanWithRelations"}, "SubscriptionWithRelations": {"title": "SubscriptionWithRelations", "type": "object", "description": "(tsType: SubscriptionWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "subscriberId": {"type": "string"}, "externalSubscriptionId": {"type": "string"}, "priceRefId": {"type": "string", "description": "price reference ID of the subscription."}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "numberOfUsers": {"type": "number", "description": "number of users for the tenant"}, "totalCost": {"type": "number", "description": "total cost of the tenant"}, "status": {"type": "number", "description": "status of the subscription, it can be - 0(pending), 1(active), 2(inactive), 3(cancelled) and 4(expired)", "enum": [0, 1, 2, 3, 4]}, "planId": {"type": "string", "description": "plan id of the subscription"}, "plan": {"$ref": "#/components/schemas/PlanWithRelations"}, "foreignKey": {}}, "required": ["subscriberId", "externalSubscriptionId", "priceRefId", "totalCost", "status"], "additionalProperties": false, "x-typescript-type": "SubscriptionWithRelations"}, "Plan": {"title": "Plan", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the plan"}, "description": {"type": "string", "description": "description of the plan"}, "tier": {"type": "string", "description": "Tier of the plan.", "enum": ["premium", "standard"]}, "price": {"type": "number"}, "metaData": {"type": "object", "description": "Meta data of the plan"}, "status": {"type": "number", "description": "Status of the plan.", "enum": [0, 1]}, "productRefId": {"type": "string", "description": "product reference ID of the plan."}, "version": {"type": "string", "description": "current version of the plan."}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "billingCycleId": {"type": "string"}, "currencyId": {"type": "string"}, "configureDeviceId": {"type": "string"}, "planSizeId": {"type": "string"}}, "required": ["name", "tier", "price", "status", "productRefId", "version"], "additionalProperties": false}, "NewPlan": {"title": "NewPlan", "type": "object", "description": "(tsType: Omit<Plan, 'id' | 'status' | 'version' | 'planSizeId' | 'productRefId'>, schemaOptions: { title: 'NewPlan', exclude: [ 'id', 'status', 'version', 'planSizeId', 'productRefId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "name": {"type": "string", "description": "name of the plan"}, "description": {"type": "string", "description": "description of the plan"}, "tier": {"type": "string", "description": "Tier of the plan.", "enum": ["premium", "standard"]}, "price": {"type": "number"}, "metaData": {"type": "object", "description": "Meta data of the plan"}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "billingCycleId": {"type": "string"}, "currencyId": {"type": "string"}, "configureDeviceId": {"type": "string"}}, "required": ["name", "tier", "price"], "additionalProperties": false, "x-typescript-type": "Omit<Plan, 'id' | 'status' | 'version' | 'planSizeId' | 'productRefId'>"}, "EditPlanDtoPartial": {"title": "EditPlanDtoPartial", "type": "object", "description": "model describing payload used to edit plan (tsType: Partial<EditPlanDto>, schemaOptions: { partial: true })", "properties": {"price": {"type": "number"}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}}, "additionalProperties": false}, "EditPlanDto": {"title": "EditPlanDto", "type": "object", "description": "model describing payload used to edit plan", "properties": {"price": {"type": "number"}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}}, "required": ["price"], "additionalProperties": false}, "PlanStatusDtoPartial": {"title": "PlanStatusDtoPartial", "type": "object", "description": "model describing payload used to verify key for a tenant (tsType: Partial<PlanStatusDto>, schemaOptions: { partial: true })", "properties": {"status": {"type": "number", "description": "Status of the plan.", "enum": [0, 1]}}, "additionalProperties": false}, "PlanStatusDto": {"title": "PlanStatusDto", "type": "object", "description": "model describing payload used to verify key for a tenant", "properties": {"status": {"type": "number", "description": "Status of the plan.", "enum": [0, 1]}}, "required": ["status"], "additionalProperties": false}, "configure_devices.Filter": {"type": "object", "title": "configure_devices.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "configure_devices.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "min": {"type": "boolean"}, "max": {"type": "boolean"}, "computeSize": {"type": "boolean"}, "dbSize": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "min", "max", "computeSize", "dbSize"], "example": "deleted"}, "uniqueItems": true}], "title": "configure_devices.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<ConfigureDevice>"}, "currencies.Filter": {"type": "object", "title": "currencies.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "currencies.Where<PERSON><PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"id": {"type": "boolean"}, "currencyCode": {"type": "boolean"}, "currencyName": {"type": "boolean"}, "symbol": {"type": "boolean"}, "country": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["id", "currencyCode", "currencyName", "symbol", "country"], "example": "id"}, "uniqueItems": true}], "title": "currencies.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Currency>"}, "loopback.Count": {"type": "object", "title": "loopback.Count", "x-typescript-type": "@loopback/repository#Count", "properties": {"count": {"type": "number"}}}, "invoice.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "invoice.ScopeFilter"}, "invoice.IncludeFilter.Items": {"title": "invoice.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["billingCustomer"]}, "scope": {"$ref": "#/components/schemas/invoice.ScopeFilter"}}}, "invoice.Filter": {"type": "object", "title": "invoice.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "invoiceId": {"type": "boolean"}, "invoiceStatus": {"type": "boolean"}, "billingCustomerId": {"type": "boolean"}, "amount": {"type": "boolean"}, "tax": {"type": "boolean"}, "discount": {"type": "boolean"}, "dueDate": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "invoiceId", "invoiceStatus", "billingCustomerId", "amount", "tax", "discount", "dueDate"], "example": "deleted"}, "uniqueItems": true}], "title": "invoice.Fields"}, "include": {"title": "invoice.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/invoice.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Invoice>"}, "invoice.Filter1": {"type": "object", "title": "invoice.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "invoice.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "invoiceId": {"type": "boolean"}, "invoiceStatus": {"type": "boolean"}, "billingCustomerId": {"type": "boolean"}, "amount": {"type": "boolean"}, "tax": {"type": "boolean"}, "discount": {"type": "boolean"}, "dueDate": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "invoiceId", "invoiceStatus", "billingCustomerId", "amount", "tax", "discount", "dueDate"], "example": "deleted"}, "uniqueItems": true}], "title": "invoice.Fields"}, "include": {"title": "invoice.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/invoice.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Invoice>"}, "PingResponse": {"type": "object", "title": "PingResponse", "properties": {"greeting": {"type": "string"}, "date": {"type": "string"}, "url": {"type": "string"}, "headers": {"type": "object", "properties": {"Content-Type": {"type": "string"}}, "additionalProperties": true}}}, "plan_history.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "plan_history.ScopeFilter"}, "plan_history.IncludeFilter.Items": {"title": "plan_history.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["plan"]}, "scope": {"$ref": "#/components/schemas/plan_history.ScopeFilter"}}}, "plan_history.Filter": {"type": "object", "title": "plan_history.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "plan_history.Where<PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "price": {"type": "boolean"}, "version": {"type": "boolean"}, "allowedUnlimitedUsers": {"type": "boolean"}, "costPerUser": {"type": "boolean"}, "planId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "price", "version", "allowedUnlimitedUsers", "costPerUser", "planId"], "example": "deleted"}, "uniqueItems": true}], "title": "plan_history.Fields"}, "include": {"title": "plan_history.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/plan_history.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<PlanHistory>"}, "plan_sizes.Filter": {"type": "object", "title": "plan_sizes.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "plan_sizes.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "size": {"type": "boolean"}, "config": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "size", "config"], "example": "deleted"}, "uniqueItems": true}], "title": "plan_sizes.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<PlanSizes>"}, "plans.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "plans.<PERSON>ope<PERSON><PERSON>er"}, "plans.IncludeFilter.Items": {"title": "plans.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["billingCycle", "currency", "configureDevice", "planSize", "planHistories"]}, "scope": {"$ref": "#/components/schemas/plans.ScopeFilter"}}}, "plans.Filter": {"type": "object", "title": "plans.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "description": {"type": "boolean"}, "tier": {"type": "boolean"}, "price": {"type": "boolean"}, "metaData": {"type": "boolean"}, "status": {"type": "boolean"}, "productRefId": {"type": "boolean"}, "version": {"type": "boolean"}, "allowedUnlimitedUsers": {"type": "boolean"}, "costPerUser": {"type": "boolean"}, "billingCycleId": {"type": "boolean"}, "currencyId": {"type": "boolean"}, "configureDeviceId": {"type": "boolean"}, "planSizeId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "description", "tier", "price", "metaData", "status", "productRefId", "version", "allowedUnlimitedUsers", "costPerUser", "billingCycleId", "currencyId", "configureDeviceId", "planSizeId"], "example": "deleted"}, "uniqueItems": true}], "title": "plans.Fields"}, "include": {"title": "plans.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/plans.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Plan>"}, "plans.Filter1": {"type": "object", "title": "plans.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "plans.Where<PERSON><PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "description": {"type": "boolean"}, "tier": {"type": "boolean"}, "price": {"type": "boolean"}, "metaData": {"type": "boolean"}, "status": {"type": "boolean"}, "productRefId": {"type": "boolean"}, "version": {"type": "boolean"}, "allowedUnlimitedUsers": {"type": "boolean"}, "costPerUser": {"type": "boolean"}, "billingCycleId": {"type": "boolean"}, "currencyId": {"type": "boolean"}, "configureDeviceId": {"type": "boolean"}, "planSizeId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "description", "tier", "price", "metaData", "status", "productRefId", "version", "allowedUnlimitedUsers", "costPerUser", "billingCycleId", "currencyId", "configureDeviceId", "planSizeId"], "example": "deleted"}, "uniqueItems": true}], "title": "plans.Fields"}, "include": {"title": "plans.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/plans.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Plan>"}, "subscriptions.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "subscriptions.ScopeFilter"}, "subscriptions.IncludeFilter.Items": {"title": "subscriptions.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["plan"]}, "scope": {"$ref": "#/components/schemas/subscriptions.ScopeFilter"}}}, "subscriptions.Filter": {"type": "object", "title": "subscriptions.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "subscriptions.Where<PERSON><PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "subscriberId": {"type": "boolean"}, "externalSubscriptionId": {"type": "boolean"}, "priceRefId": {"type": "boolean"}, "startDate": {"type": "boolean"}, "endDate": {"type": "boolean"}, "numberOfUsers": {"type": "boolean"}, "totalCost": {"type": "boolean"}, "status": {"type": "boolean"}, "planId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "subscriberId", "externalSubscriptionId", "priceRefId", "startDate", "endDate", "numberOfUsers", "totalCost", "status", "planId"], "example": "deleted"}, "uniqueItems": true}], "title": "subscriptions.Fields"}, "include": {"title": "subscriptions.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/subscriptions.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Subscription>"}, "billing_cycles.Filter": {"type": "object", "title": "billing_cycles.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "billing_cycles.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "cycleName": {"type": "boolean"}, "duration": {"type": "boolean"}, "durationUnit": {"type": "boolean"}, "description": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "cycleName", "duration", "durationUnit", "description"], "example": "deleted"}, "uniqueItems": true}], "title": "billing_cycles.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<BillingCycle>"}, "tenant_billings_view.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "tenant_billings_view.ScopeFilter"}, "tenant_billings_view.IncludeFilter.Items": {"title": "tenant_billings_view.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["billingCustomer"]}, "scope": {"$ref": "#/components/schemas/tenant_billings_view.ScopeFilter"}}}, "tenant_billings_view.Filter": {"type": "object", "title": "tenant_billings_view.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "tenant_billings_view.WhereF<PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "invoiceId": {"type": "boolean"}, "invoiceStatus": {"type": "boolean"}, "billingCustomerId": {"type": "boolean"}, "amount": {"type": "boolean"}, "tax": {"type": "boolean"}, "discount": {"type": "boolean"}, "dueDate": {"type": "boolean"}, "customerId": {"type": "boolean"}, "tenantId": {"type": "boolean"}, "tenantName": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "invoiceId", "invoiceStatus", "billingCustomerId", "amount", "tax", "discount", "dueDate", "customerId", "tenantId", "tenantName"], "example": "deleted"}, "uniqueItems": true}], "title": "tenant_billings_view.Fields"}, "include": {"title": "tenant_billings_view.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/tenant_billings_view.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<TenantBillingsView>"}, "ContactExcluding_tenantId-id-email_": {"type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: Omit<Contact, 'tenantId' | 'id' | 'email'>, schemaOptions: { exclude: [ 'tenantId', 'id', 'email' ] })", "title": "ContactExcluding_tenantId-id-email_", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "lastName": {"type": "string", "description": "last name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "designation": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "phoneNumber": {"type": "string", "minLength": 10, "maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Invalid phone number format. Use only digits.", "minLength": "Phone number must be at least 10 digits long.", "maxLength": "Phone number cannot exceed 10 characters."}}, "countryCode": {"type": "string", "minLength": 2, "maxLength": 5, "pattern": "^\\+[0-9]{1,4}$", "errorMessage": {"pattern": "Invalid country code format. It should start with an + followed by 1 to 4 digits.", "minLength": "Country code must be at least 2 characters.", "maxLength": "Country code cannot exceed 5 characters."}}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}}, "required": ["firstName", "lastName", "isPrimary"], "additionalProperties": false}, "ContactExcluding_tenantId-id_": {"type": "object", "description": "main model of the service that represents a tenant in the system, either pooled or siloed (tsType: Omit<Contact, 'tenantId' | 'id'>, schemaOptions: { exclude: [ 'tenantId', 'id' ] })", "title": "ContactExcluding_tenantId-id_", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "firstName": {"type": "string", "description": "first name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "lastName": {"type": "string", "description": "last name of the tenant admin", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "email": {"type": "string", "description": "email id of the contact", "format": "email", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,7}$", "errorMessage": {"pattern": "Invalid email format. Please provide a valid email address."}}, "designation": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]+$"}, "phoneNumber": {"type": "string", "minLength": 10, "maxLength": 10, "pattern": "^\\d{10}$", "errorMessage": {"pattern": "Invalid phone number format. Use only digits.", "minLength": "Phone number must be at least 10 digits long.", "maxLength": "Phone number cannot exceed 10 characters."}}, "countryCode": {"type": "string", "minLength": 2, "maxLength": 5, "pattern": "^\\+[0-9]{1,4}$", "errorMessage": {"pattern": "Invalid country code format. It should start with an + followed by 1 to 4 digits.", "minLength": "Country code must be at least 2 characters.", "maxLength": "Country code cannot exceed 5 characters."}}, "isPrimary": {"type": "boolean", "description": "boolean value denoting if the contact is a primary contact for it's tenant."}, "type": {"type": "string", "description": "type of the contact"}}, "required": ["firstName", "lastName", "email", "isPrimary"], "additionalProperties": false}, "tenants.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "tenants.ScopeFilter"}, "tenants.IncludeFilter.Items": {"title": "tenants.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["contacts", "resources", "lead", "address", "files"]}, "scope": {"$ref": "#/components/schemas/tenants.ScopeFilter"}}}, "tenants.Filter": {"type": "object", "title": "tenants.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "tenants.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "status": {"type": "boolean"}, "key": {"type": "boolean"}, "spocUserId": {"type": "boolean"}, "domains": {"type": "boolean"}, "leadId": {"type": "boolean"}, "addressId": {"type": "boolean"}, "lang": {"type": "boolean"}, "planName": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "status", "key", "spocUserId", "domains", "leadId", "addressId", "lang", "planName"], "example": "deleted"}, "uniqueItems": true}], "title": "tenants.Fields"}, "include": {"title": "tenants.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/tenants.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Tenant>"}}}, "servers": [{"url": "/"}]}