/* eslint-disable @typescript-eslint/naming-convention */
import {inject} from '@loopback/core';
import Stripe from 'stripe';
import {
  StripeBindings,
  StripeConfig,
  StripeService as BaseService,
  TInvoice,
  IStripeCustomer,
  TCustomer,
} from 'loopback4-billing';
import {InvoiceStatus, PriceDto} from '@local/core';
import {AnyObject} from '@loopback/repository';
import {
  IBillingService,
  InvoicePrice,
  ProductCreateParams,
  PlanCreateParams,
  SubscriptionCreateParams,
  IStripeInvoice,
} from '../types';

const MILLISECONDS_TO_SECONDS = 1000;

/**
 * Implementation of the billing service using Stripe.
 *
 * This service extends the base Stripe service from `loopback4-billing`
 * and provides custom methods to handle products, plans, subscriptions,
 * invoices, and customers.
 */
export class StripeService extends BaseService implements IBillingService {
  private stripeInstance: Stripe;

  /**
   * Creates an instance of {@link StripeService}.
   *
   * @param _stripeConfig - Stripe configuration (injected from bindings).
   */
  constructor(
    @inject(StripeBindings.config, {optional: true})
    private readonly _stripeConfig: StripeConfig,
  ) {
    super(_stripeConfig);
    this.stripeInstance = new Stripe(_stripeConfig.secretKey ?? '', {
      apiVersion: '2025-07-30.basil', // Update to latest API version as needed
    });
  }

  /**
   * Creates a new price in Stripe.
   *
   * @param price - The price DTO containing price details.
   * @returns The created price as a {@link PriceDto}.
   */
  async createPrice(price: PriceDto): Promise<PriceDto> {
    const createdPrice = await this.stripeInstance.prices.create({
      ...price,
      recurring: price.recurring
        ? {
            ...price.recurring,
            interval: price.recurring
              .interval as Stripe.PriceCreateParams.Recurring.Interval,
            aggregate_usage: price.recurring
              .aggregate_usage as Stripe.PriceCreateParams.Recurring.AggregateUsage,
          }
        : undefined,
      metadata: price.metadata
        ? Object.fromEntries(
            Object.entries(price.metadata).map(([k, v]) => [
              k,
              typeof v === 'string' || typeof v === 'number' || v === null
                ? v
                : String(v),
            ]),
          )
        : undefined,
    });
    return new PriceDto({
      id: createdPrice.id,
      active: createdPrice.active,
      currency: createdPrice.currency,
      metadata: createdPrice.metadata,
      product:
        typeof createdPrice.product === 'string'
          ? createdPrice.product
          : createdPrice.product?.id,
      recurring: createdPrice.recurring
        ? {
            ...createdPrice.recurring,
            aggregate_usage:
              createdPrice.recurring.aggregate_usage ?? undefined,
            meter: createdPrice.recurring.meter ?? undefined,
          }
        : undefined,
      unit_amount: createdPrice.unit_amount ?? undefined,
    });
  }

  /**
   * Retrieves a payment intent by ID from Stripe.
   *
   * @param paymentIntentId - The ID of the payment intent.
   * @returns The payment intent object.
   */
  async retrievePaymentIntent(paymentIntentId: string): Promise<AnyObject> {
    return this.stripeInstance.paymentIntents.retrieve(paymentIntentId);
  }

  /**
   * Creates an invoice in Stripe, including invoice items,
   * and finalizes it for payment.
   *
   * @param invoice - The invoice details.
   * @returns The created and finalized invoice model.
   * @throws Error if invoice ID is undefined.
   */
  async createInvoice(invoice: IStripeInvoice): Promise<IStripeInvoice> {
    const createdInvoice = await this.stripeInstance.invoices.create({
      customer: invoice.customerId,
      auto_advance: invoice.options?.autoAdvnace ?? false, // Optional
      shipping_details: {
        address: {
          city: invoice.shippingAddress?.city,
          country: invoice.shippingAddress?.country,
          line1: invoice.shippingAddress?.line1,
          line2:
            invoice.shippingAddress?.line2 +
            ' ' +
            invoice.shippingAddress?.line3,
          postal_code: invoice.shippingAddress?.zip,
          state: invoice.shippingAddress?.state,
        },
        name: invoice.customerId,
      },
      due_date: invoice.dueDate
        ? Math.floor(Date.parse(invoice.dueDate) / MILLISECONDS_TO_SECONDS)
        : undefined,
    });
    const promiseArr = invoice.charges?.map(lineItem =>
      this.stripeInstance.invoiceItems.create({
        customer: invoice.customerId,
        amount: lineItem.amount,
        currency: invoice.currencyCode,
        description: lineItem.description,
        invoice: createdInvoice.id,
      }),
    );
    if (promiseArr?.length) {
      await Promise.all(promiseArr);
    }
    if (!createdInvoice.id) {
      throw new Error('Invoice ID is undefined');
    }
    const finalizedInvoice = await this.stripeInstance.invoices.finalizeInvoice(
      createdInvoice.id,
    );

    return this.stripeInvoiceAdapter.adaptToModel(finalizedInvoice);
  }

  /**
   * Retrieves an invoice by ID from Stripe.
   *
   * @param invoiceId - The ID of the invoice.
   * @returns The invoice model.
   */
  async retrieveInvoice(invoiceId: string): Promise<TInvoice> {
    const invoice = await this.stripeInstance.invoices.retrieve(invoiceId);
    return this.stripeInvoiceAdapter.adaptToModel(invoice);
  }

  /**
   * Retrieves the PDF download URL for a Stripe invoice.
   *
   * @param invoiceId - The ID of the invoice.
   * @returns The invoice PDF URL, or null if not available.
   */
  async getInvoicePdfUrl(invoiceId: string): Promise<string | null> {
    const invoice = await this.stripeInstance.invoices.retrieve(invoiceId);
    // The invoice_pdf property contains the URL to download the PDF
    // See: https://stripe.com/docs/api/invoices/object#invoice_object-invoice_pdf
    return invoice?.invoice_pdf ?? null;
  }

  /**
   * Retrieves detailed price information for an invoice,
   * including tax and amount excluding tax.
   *
   * @param invoiceId - The ID of the invoice.
   * @returns An object containing invoice price details.
   */
  async getInvoicePriceDetails(invoiceId: string): Promise<InvoicePrice> {
    const invoice = await this.stripeInstance.invoices.retrieve(invoiceId);
    const taxAmount =
      invoice.total_tax_amounts?.reduce((sum, tax) => sum + tax.amount, 0) || 0;

    const amountExcludingTax = invoice.total - taxAmount;

    return {
      currency: invoice.currency.toUpperCase(),
      totalAmount: invoice.total,
      taxAmount: taxAmount,
      amountExcludingTax: amountExcludingTax,
    };
  }

  /**
   * Creates a new product in Stripe.
   *
   * @param product - The product details.
   * @returns The created product ID.
   */
  async createProduct(product: ProductCreateParams): Promise<string> {
    const newProduct = await this.stripeInstance.products.create({
      name: product.name,
      description: product.description,
      metadata: product.metadata,
    });
    return newProduct.id;
  }

  /**
   * Creates a new plan in Stripe.
   *
   * @param plan - The plan details.
   * @returns The created plan ID.
   */
  async createPlan(plan: PlanCreateParams): Promise<string> {
    const newPlan = await this.stripeInstance.plans.create({
      product: plan.productId,
      amount: plan.amount,
      currency: plan.currency,
      interval: plan.recurringInterval,
      interval_count: plan.recurringCount,
      metadata: plan.metadata,
    });
    return newPlan.id;
  }

  /**
   * Creates a new subscription in Stripe.
   *
   * @param subscription - The subscription details.
   * @returns The created subscription ID.
   */
  async createSubscription(
    subscription: SubscriptionCreateParams,
  ): Promise<string> {
    const newSubscription = await this.stripeInstance.subscriptions.create({
      customer: subscription.customerId,
      items: [{price: subscription.priceRefId}],
      collection_method: subscription.collectionMethod,
      days_until_due: subscription.daysUntilDue,
      payment_behavior: 'default_incomplete',
    });
    return newSubscription.id;
  }

  async updateSubscription(
    subscriptionId: string,
    updates: Partial<SubscriptionCreateParams>,
  ): Promise<Stripe.Subscription> {
    try {
      const subscribtion = await this.getSubscription(subscriptionId);
      const priceItemId = subscribtion.items.data[0].id;
      const updatedSubscription =
        await this.stripeInstance.subscriptions.update(subscriptionId, {
          proration_behavior: 'always_invoice', // set to always_invoice to always make the immediate payment for updates
          items: [{id: priceItemId, price: updates.priceRefId}],
        });
      return updatedSubscription;
    } catch (error) {
      console.info(`Error updating subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

  /**
   * Retrieves a subscription by ID from Stripe.
   *
   * @param subscriptionId - The subscription ID.
   * @returns The subscription object.
   * @throws Error if retrieval fails.
   */
  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      const subscription =
        await this.stripeInstance.subscriptions.retrieve(subscriptionId);
      return subscription;
    } catch (error) {
      console.info(`Error fetching subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

  /**
   * Creates a new customer in Stripe.
   *
   * @param customerDto - The customer details DTO.
   * @returns The created customer model.
   */
  async createCustomer(customerDto: TCustomer): Promise<IStripeCustomer> {
    const customer = await this.stripeInstance.customers.create({
      name: `${customerDto.firstName} ${customerDto.lastName}`,
      email: customerDto.email,
      phone: customerDto.phone,
    });
    return this.stripeCustomerAdapter.adaptToModel(customer);
  }

  /**
   * Cancels a subscription in Stripe and voids related invoices.
   *
   * @param subscriptionId - The subscription ID.
   */
  async cancelSubscription(subscriptionId: string): Promise<void> {
    await this.stripeInstance.subscriptions.cancel(subscriptionId);
    const invoices = await this.stripeInstance.invoices.list({
      subscription: subscriptionId,
    });
    const voidPromises = invoices.data.map(async invoice => {
      if (invoice.status === InvoiceStatus.OPEN) {
        if (invoice.id) {
          return this.stripeInstance.invoices.voidInvoice(invoice.id);
        }
        return Promise.resolve();
      } else if (invoice.status === InvoiceStatus.DRAFT) {
        if (invoice.id) {
          await this.finalizeAndVoidInvoice(invoice.id);
        }
      } else {
        // Do Nothing
      }
      // No-op for invoices that can't be voided
      return Promise.resolve();
    });
    await Promise.all(voidPromises);
  }

  /**
   * Sends a payment link for a given invoice.
   *
   * @param invoiceId - The invoice ID.
   */
  async sendPaymentLink(invoiceId: string) {
    await this.stripeInstance.invoices.sendInvoice(invoiceId);
  }

  /**
   * Finalizes and voids a given invoice.
   *
   * @param invoiceId - The invoice ID.
   */
  private async finalizeAndVoidInvoice(invoiceId: string) {
    await this.finalizeInvoice(invoiceId);
    await this.voidInvoice(invoiceId);
  }

  /**
   * Voids a given invoice.
   *
   * @param invoiceId - The invoice ID.
   */
  private async voidInvoice(invoiceId: string) {
    await this.stripeInstance.invoices.voidInvoice(invoiceId);
  }

  /**
   * Finalizes a given invoice.
   *
   * @param invoiceId - The invoice ID.
   */
  private async finalizeInvoice(invoiceId: string) {
    await this.stripeInstance.invoices.finalizeInvoice(invoiceId);
  }
}
