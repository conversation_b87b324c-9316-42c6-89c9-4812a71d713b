import {expect} from '@loopback/testlab';
import {StripeService} from '../../services/stripe.service';
import {StripeConfig} from 'loopback4-billing';
import sinon from 'sinon';
import Stripe from 'stripe';
import {CollectionMethod, RecurringInterval} from '../../types';
import {PriceDto} from '@local/core';

const mockInvoice = {
  id: 'inv_123',
  total: 1000,
  currency: 'usd',
} as Stripe.Invoice;

describe('StripeService', () => {
  let stripeService: StripeService;
  const invoiceCreateStub = sinon.stub().resolves({id: 'inv_123'});
  const invoiceFinalizeStub = sinon.stub().resolves({id: 'inv_123'});
  let stripeMock: sinon.SinonStubbedInstance<Stripe>;

  beforeEach(() => {
    const mockConfig: StripeConfig = {
      secretKey: 'test_secret_key',
    };

    // Create a fake Stripe instance
    stripeMock = {
      products: {
        create: sinon.stub().resolves({id: 'prod_123'}),
      },
      plans: {
        create: sinon.stub().resolves({id: 'plan_123'}),
      },
      prices: {
        create: sinon
          .stub()
          //eslint-disable-next-line @typescript-eslint/naming-convention
          .resolves({id: 'price_123', currency: 'usd', unit_amount: 1000}),
      },
      paymentIntents: {
        retrieve: sinon.stub().resolves({id: 'pi_123', amount: 1000}),
      },
      subscriptions: {
        create: sinon.stub().resolves({id: 'sub_123'}),
        cancel: sinon.stub().resolves(),
        update: sinon.stub().resolves({id: 'sub_123'}), // ensure update exists
        retrieve: sinon.stub().resolves({
          id: 'sub_123',
          items: {data: [{id: 'si_123', price: {id: 'price_old'}}]},
        }),
      },
      invoices: {
        create: invoiceCreateStub,
        retrieve: sinon.stub().resolves(mockInvoice),
        finalizeInvoice: invoiceFinalizeStub,
        list: sinon.stub().resolves({data: []}),
        voidInvoice: sinon.stub().resolves(),
        sendInvoice: sinon.stub().resolves(),
      },
      invoiceItems: {
        create: sinon.stub().resolves({id: 'item_123'}),
      },
      customers: {
        create: sinon.stub().resolves({
          id: 'cus_123',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '1234567890',
        }),
      },
    } as unknown as sinon.SinonStubbedInstance<Stripe>;

    // Inject mock Stripe instance into service
    stripeService = new StripeService(mockConfig);
    (
      stripeService as unknown as {
        stripeInstance: sinon.SinonStubbedInstance<Stripe>;
      }
    ).stripeInstance = stripeMock;
  });

  afterEach(() => {
    sinon.restore(); // Restore all mocks
  });

  it('should create a price and return PriceDto-like object', async () => {
    // Arrange
    const priceDto: PriceDto = new PriceDto({
      currency: 'usd',
      //eslint-disable-next-line @typescript-eslint/naming-convention
      unit_amount: 1000,
      recurring: {interval: 'month'},
      metadata: {tier: 'basic'},
    });

    // Act
    const created = await stripeService.createPrice(priceDto);

    // Assert
    expect(created).to.have.property('id', 'price_123');
    expect(created).to.have.property('currency', 'usd');
    sinon.assert.calledOnce(stripeMock.prices.create as sinon.SinonStub);
  });

  it('should retrieve a payment intent by id', async () => {
    // Arrange
    (stripeMock.paymentIntents!.retrieve as sinon.SinonStub).resolves({
      id: 'pi_abc',
      amount: 5000,
    });

    // Act
    const pi = await stripeService.retrievePaymentIntent('pi_abc');

    // Assert
    expect(pi.id).to.equal('pi_abc');
    sinon.assert.calledWith(
      stripeMock.paymentIntents!.retrieve as sinon.SinonSpy,
      'pi_abc',
    );
  });

  it('should get subscription successfully via getSubscription', async () => {
    // Arrange
    const stubSub = {
      id: 'sub_123',
      items: {data: [{id: 'si_123'}]},
    } as unknown as Stripe.Subscription;
    (stripeMock.subscriptions.retrieve as sinon.SinonStub).resolves(stubSub);

    // Act
    const result = await stripeService.getSubscription('sub_123');

    // Assert
    expect(result).to.have.property('id', 'sub_123');
    sinon.assert.calledOnce(
      stripeMock.subscriptions.retrieve as sinon.SinonStub,
    );
  });

  it('should throw when getSubscription fails', async () => {
    // Arrange
    (stripeMock.subscriptions.retrieve as sinon.SinonStub).rejects(
      new Error('not found'),
    );

    // Act & Assert
    await expect(stripeService.getSubscription('bad_sub')).to.be.rejectedWith(
      'not found',
    );
    sinon.assert.calledOnce(
      stripeMock.subscriptions.retrieve as sinon.SinonStub,
    );
  });

  it('should throw when created invoice has no id', async () => {
    // Arrange: make invoices.create return an object without id
    (stripeMock.invoices.create as sinon.SinonStub).resolves({});

    // Act & Assert
    await expect(
      stripeService.createInvoice({
        customerId: 'cus_123',
        options: {autoAdvnace: true},
        shippingAddress: {
          city: 'City',
          country: 'Country',
          line1: 'Line 1',
          line2: 'Line 2',
          line3: 'Line 3',
          zip: '12345',
          state: 'State',
        },
        dueDate: new Date().toISOString(),
        charges: [{amount: 1000, description: 'Test charge'}],
        currencyCode: 'usd',
      }),
    ).to.be.rejectedWith('Invoice ID is undefined');
    sinon.assert.calledOnce(stripeMock.invoices.create as sinon.SinonStub);
  });

  it('should create a product', async () => {
    const mockProduct = {id: 'prod_123'};
    (stripeMock.products.create as sinon.SinonStub).resolves(mockProduct);

    const productId = await stripeService.createProduct({
      name: 'Test Product',
      description: 'Test description',
      metadata: {key: 'value'},
      priceData: {
        currency: 'usd',
        unitAmount: 1000,
        recurringInterval: RecurringInterval.MONTH,
        recurringCount: 1,
      },
    });

    expect(productId).to.equal('prod_123');
    sinon.assert.calledOnce(stripeMock.products.create as sinon.SinonSpy);
  });

  it('should update subscription with new price and return updated subscription', async () => {
    const mockExistingSubscription: Partial<Stripe.Subscription> = {
      id: 'sub_123',
      items: {
        data: [
          {
            id: 'si_123', // subscription item id that updateSubscription will use
            price: {id: 'price_old'} as Stripe.Price,
          },
        ],
      } as Stripe.ApiList<Stripe.SubscriptionItem>,
    };

    const mockUpdatedSubscription: Partial<Stripe.Subscription> = {
      id: 'sub_123',
      items: {
        data: [
          {
            id: 'si_123',
            price: {id: 'price_new'} as Stripe.Price,
          },
        ],
      } as Stripe.ApiList<Stripe.SubscriptionItem>,
    };

    // Stub the instance method getSubscription on the service to return existing subscription
    (
      stripeService as unknown as {getSubscription: sinon.SinonStub}
    ).getSubscription = sinon
      .stub()
      .resolves(mockExistingSubscription as Stripe.Subscription);

    // Stub stripe.subscriptions.update to return updated subscription
    (stripeMock.subscriptions.update as sinon.SinonStub).resolves(
      mockUpdatedSubscription as Stripe.Subscription,
    );

    const result = await stripeService.updateSubscription('sub_123', {
      priceRefId: 'price_new',
    });

    // should return the updated subscription object
    expect(result).to.have.property('id', 'sub_123');
    expect(result.items.data[0].price?.id).to.equal('price_new');

    // ensure getSubscription was called to fetch current subscription
    sinon.assert.calledOnce(
      (stripeService as unknown as {getSubscription: sinon.SinonStub})
        .getSubscription,
    );

    // ensure stripe.subscriptions.update was called with correct args
    const priceItemId = mockExistingSubscription.items!.data[0].id;
    sinon.assert.calledWith(
      stripeMock.subscriptions.update as sinon.SinonSpy,
      'sub_123',
      sinon.match({
        //eslint-disable-next-line @typescript-eslint/naming-convention
        proration_behavior: 'always_invoice',
        items: [{id: priceItemId, price: 'price_new'}],
      }),
    );
  });

  it('should create a subscription', async () => {
    const mockSubscription = {id: 'sub_123'};
    (stripeMock.subscriptions.create as sinon.SinonStub).resolves(
      mockSubscription,
    );

    const subscriptionId = await stripeService.createSubscription({
      customerId: 'cus_123',
      priceRefId: 'plan_123',
      collectionMethod: CollectionMethod.SEND_INVOICE,
      daysUntilDue: 7,
    });

    expect(subscriptionId).to.equal('sub_123');
    sinon.assert.calledOnce(stripeMock.subscriptions.create as sinon.SinonSpy);
  });

  it('should retrieve an invoice', async () => {
    (stripeMock.invoices.retrieve as sinon.SinonStub).resolves(mockInvoice);

    const invoice = await stripeService.retrieveInvoice('inv_123');

    expect(invoice).to.have.property('id', 'inv_123');
    expect(invoice).to.have.property('currencyCode', 'usd');
    sinon.assert.calledOnce(stripeMock.invoices.retrieve as sinon.SinonSpy);
  });

  it('should get invoice price details', async () => {
    const mockInvoiceDto = {
      id: 'inv_123',
      total: 2000,
      currency: 'usd',
      //eslint-disable-next-line @typescript-eslint/naming-convention
      total_tax_amounts: [{amount: 200}],
    } as Stripe.Invoice;
    (stripeMock.invoices.retrieve as sinon.SinonStub).resolves(mockInvoiceDto);

    const priceDetails = await stripeService.getInvoicePriceDetails('inv_123');

    expect(priceDetails.currency).to.equal('USD');
    expect(priceDetails.totalAmount).to.equal(2000);
    expect(priceDetails.taxAmount).to.equal(200);
    expect(priceDetails.amountExcludingTax).to.equal(1800);
    sinon.assert.calledOnce(stripeMock.invoices.retrieve as sinon.SinonSpy);
  });

  it('should cancel subscription and void invoices correctly', async () => {
    const mockInvoices = {
      data: [
        {id: 'inv1', status: 'open'},
        {id: 'inv2', status: 'draft'},
        {id: 'inv3', status: 'paid'},
      ],
    };

    stripeMock.invoices.list = sinon.stub().resolves(mockInvoices);
    const voidStub = sinon.stub();
    const finalizeStub = sinon.stub();

    (stripeService as unknown as {voidInvoice: sinon.SinonStub}).voidInvoice =
      voidStub;
    (
      stripeService as unknown as {finalizeInvoice: sinon.SinonStub}
    ).finalizeInvoice = finalizeStub;

    await stripeService.cancelSubscription('sub_123');

    sinon.assert.calledWith(
      stripeMock.subscriptions.cancel as sinon.SinonSpy,
      'sub_123',
    );
    sinon.assert.calledWith(voidStub, 'inv2');
    sinon.assert.calledWith(finalizeStub, 'inv2');
  });

  it('should send a payment link', async () => {
    stripeMock.invoices.sendInvoice = sinon.stub().resolves();
    await stripeService.sendPaymentLink('inv_123');
    sinon.assert.calledWith(
      stripeMock.invoices.sendInvoice as sinon.SinonSpy,
      'inv_123',
    );
  });

  it('should create a customer', async () => {
    (stripeService as unknown as {voidInvoice: sinon.SinonStub}).voidInvoice =
      sinon.stub().resolves();

    await stripeService.createCustomer({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '1234567890',
    });

    sinon.assert.calledOnce(stripeMock.customers.create as sinon.SinonSpy);
  });

  it('should create a plan', async () => {
    stripeMock.plans.create = sinon.stub().resolves({id: 'plan_123'});

    const result = await stripeService.createPlan({
      productId: 'prod_123',
      amount: 1000,
      currency: 'usd',
      recurringInterval: RecurringInterval.MONTH,
      recurringCount: 1,
      metadata: {tier: 'basic'},
    });

    expect(result).to.equal('plan_123');
  });
  it('should return invoice PDF url from getInvoicePdfUrl', async () => {
    const pdfUrl = 'https://stripe.com/invoice/inv_123.pdf';
    (stripeMock.invoices.retrieve as sinon.SinonStub).resolves({
      id: 'inv_123',
      //eslint-disable-next-line @typescript-eslint/naming-convention
      invoice_pdf: pdfUrl,
    } as Stripe.Invoice);

    const result = await stripeService.getInvoicePdfUrl('inv_123');
    expect(result).to.equal(pdfUrl);
    sinon.assert.calledOnce(stripeMock.invoices.retrieve as sinon.SinonSpy);
  });

  it('should return null if invoice PDF url is not present', async () => {
    (stripeMock.invoices.retrieve as sinon.SinonStub).resolves({
      id: 'inv_123',
      //eslint-disable-next-line @typescript-eslint/naming-convention
      invoice_pdf: null,
    } as Stripe.Invoice);

    const result = await stripeService.getInvoicePdfUrl('inv_123');
    expect(result).to.be.null();
    sinon.assert.calledOnce(stripeMock.invoices.retrieve as sinon.SinonSpy);
  });
});
