import {PermissionKey} from '@local/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {get, getModelSchemaRef, param} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {Invoice} from '../models';
import {InvoiceRepository} from '../repositories';
import {inject} from '@loopback/core';
import {BillingComponentBindings} from 'loopback4-billing';
import {IBillingService} from '../types';

const basePath = '/invoices';

/**
 * Controller for handling invoice operations.
 *
 * Provides endpoints to retrieve and count invoice records for tenants,
 * with authorization and authentication enforced.
 *
 * @remarks
 * - Requires `PermissionKey.ViewTenantBillings` permission for all endpoints.
 * - Uses bearer token authentication.
 *
 * @constructor
 * @param invoiceRepository - The repository instance for accessing invoice data.
 *
 * @endpoint GET /billing-invoices
 *   - Retrieves an array of invoice instances, optionally filtered.
 *
 * @endpoint GET /billing-invoices/count
 *   - Retrieves the count of invoice instances, optionally filtered by criteria.
 */
export class InvoiceController {
  constructor(
    @repository(InvoiceRepository)
    public invoiceRepository: InvoiceRepository,
    @inject(BillingComponentBindings.SDKProvider)
    public stripeService: IBillingService,
  ) {}

  @authorize({
    permissions: [PermissionKey.ViewTenantBillings],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of ConfigureDevice model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Invoice, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(Invoice) filter?: Filter<Invoice>,
  ): Promise<Invoice[]> {
    return this.invoiceRepository.find(filter);
  }

  @authorize({
    permissions: [PermissionKey.ViewTenantBillings],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Invoice model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Invoice) where?: Where<Invoice>): Promise<Count> {
    return this.invoiceRepository.count(where);
  }

  /**
   * Retrieves an invoice by its unique identifier.
   *
   * @param id - The unique identifier of the invoice to retrieve.
   * @param filter - Optional filter object to specify fields and relations to include, excluding the 'where' clause.
   * @returns A promise that resolves to the invoice matching the provided ID.
   */
  @authorize({
    permissions: [PermissionKey.ViewTenantBillings],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Invoice model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Invoice, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Invoice, {exclude: 'where'})
    filter?: FilterExcludingWhere<Invoice>,
  ): Promise<Invoice> {
    return this.invoiceRepository.findById(id, filter);
  }
  /**
   * Returns the Stripe invoice PDF download URL for a given invoice ID.
   * @param id - The Stripe invoice ID.
   */
  @authorize({
    permissions: [PermissionKey.DownloadInvoice],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/{id}/pdf-url`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Stripe Invoice PDF URL',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                pdfUrl: {type: 'string', nullable: true},
              },
            },
          },
        },
      },
      [STATUS_CODE.BAD_REQUEST]: {
        description: 'Missing stripeInvoiceId in request body or filter',
      },
    },
  })
  async getStripeInvoicePdfUrl(
    @param.path.string('id') id: string,
    @param.query.string('stripeInvoiceId', {required: false}) stripeInvoiceId?: string,
  ): Promise<{pdfUrl: string | null}> {
    if (!stripeInvoiceId) {
      return {pdfUrl: null};
    }
    const pdfUrl = await this.stripeService.getInvoicePdfUrl(stripeInvoiceId);
    return {pdfUrl};
  }
}
