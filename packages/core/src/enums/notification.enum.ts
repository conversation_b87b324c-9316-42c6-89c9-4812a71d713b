/**
 * Enum representing the types of notifications supported.
 */
export enum NotificationType {
  /**
   * SMS notification.
   */
  SMS,

  /**
   * Email notification.
   */
  EMAIL,

  /**
   * Push notification (e.g., mobile or browser).
   */
  PUSH,
}

/**
 * Enum representing the types of notification events.
 */
export enum NotificationEventType {
  /**
   * Event triggered for forget password flow.
   */
  ForgetPassword = 'forget_password',

  /**
   * Event triggered for sending OTP (One-Time Password).
   */
  OtpSend = 'otp_send',
  InvoicePaymentRequest = 'invoice_payment_link',
  /**
   * Event triggered for user added
   */
  UserAdded = 'user_added',
}
