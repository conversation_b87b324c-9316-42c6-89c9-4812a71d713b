export const PermissionKey = {
  CreateLead: '10200',
  UpdateLead: '10201',
  DeleteLead: '10202',
  ViewLead: '10203',
  CreateTenant: '10204',
  ProvisionTenant: '10216',
  DeprovisionTenant: '10217',
  UpdateTenant: '10205',
  DeleteTenant: '10206',
  ViewTenant: '10207',
  CreateContact: '10208',
  UpdateContact: '10209',
  DeleteContact: '10210',
  ViewContact: '10211',
  CreateInvoice: '10212',
  UpdateInvoice: '10213',
  DeleteInvoice: '10214',
  ViewInvoice: '10215',
  ViewAllStatuses: '10216',
  CreateNotification: '2',
  DownloadInvoice: 'DownloadInvoice',
  SendInvoice: 'SendInvoice',

  CreateSubscription: '7001',
  UpdateSubscription: '7002',
  DeleteSubscription: '7003',
  ViewSubscription: '7004',
  CreateTenantConfig: '10220',
  ViewTenantConfig: '10221',
  UpdateTenantConfig: '10222',
  DeleteTenantConfig: '10223',

  //Roles
  ViewRoles: '6',

  // tenant user
  ViewTenantUser: '12',
  CreateTenantUser: '13',
  UpdateTenantUser: '14',
  // notification service
  ViewNotificationTemplate: '8000',
  CreateNotificationTemplate: '8001',
  UpdateNotificationTemplate: '8002',
  DeleteNotificationTemplate: '8003',

  CreateBillingCustomer: '5321',
  CreateBillingPaymentSource: '5322',
  CreateBillingInvoice: '5323',
  GetBillingCustomer: '5324',
  GetBillingPaymentSource: '5325',
  GetBillingInvoice: '5326',
  UpdateBillingCustomer: '5327',
  UpdateBillingPaymentSource: '5328',
  UpdateBillingInvoice: '5329',
  DeleteBillingCustomer: '5331',
  DeleteBillingPaymentSource: '5332',
  DeleteBillingInvoice: '5333',

  // subscription tier
  CreateTier: '10230',
  UpdateTier: '10231',
  DeleteTier: '10232',
  ViewTier: '10233',

  //plan-items
  CreatePlanItems: 'CreatePlanItems',
  ViewPlanItems: 'ViewPlanItems',
  UpdatePlanItems: 'UpdatePlanItems',
  DeletePlanItems: 'DeletePlanItems',

  //tenant logs
  CreateTenantLogs: 'CreateTenantLogs',
  ViewTenantLogs: 'ViewTenantLogs',
  UpdateTenantLogs: 'UpdateTenantLogs',
  DeleteTenantLogs: 'DeleteTenantLogs',

  //cluster
  CreateCluster: 'CreateCluster',
  ViewCluster: 'ViewCluster',
  UpdateCluster: 'UpdateCluster',
  DeleteCluster: 'DeleteCluster',

  //features
  ViewFeature: 'ViewFeature',
  CreateFeature: 'CreateFeature',
  UpdateFeature: 'UpdateFeature',
  DeleteFeature: 'DeleteFeature',

  ViewStrategy: 'ViewStrategy',
  CreateStrategy: 'CreateStrategy',
  UpdateStrategy: 'UpdateStrategy',
  DeleteStrategy: 'DeleteStrategy',

  ViewFeatureValues: 'ViewFeatureValues',
  CreateFeatureValues: 'CreateFeatureValues',
  UpdateFeatureValues: 'UpdateFeatureValues',
  DeleteFeatureValues: 'DeleteFeatureValues',

  ViewFeatureNum: '1',
  CreateFeatureNum: '13',
  UpdateFeatureNum: '3',
  DeleteFeatureNum: '4',

  ViewStrategyNum: '5',
  CreateStrategyNum: '6',
  UpdateStrategyNum: '7',
  DeleteStrategyNum: '8',

  ViewFeatureValuesNum: '9',
  CreateFeatureValuesNum: '10',
  UpdateFeatureValuesNum: '11',
  DeleteFeatureValuesNum: '12',
  CreatePayment: '5334',
  UpdatePayment: '5335',
  DeletePayment: '5336',
  ViewPayment: '5337',

  ViewPlan: '7008',
  CreatePlan: '7009',
  UpdatePlan: '7010',
  DeletePlan: '7011',

  ViewPrice: '7012',
  CreatePrice: '7013',
  UpdatePrice: '7014',
  DeletePrice: '7015',

  CreateClusterType: '10234',
  UpdateClusterType: '10235',
  DeleteClusterType: '10236',
  ViewClusterType: '10237',

  CreateTags: '10238',
  UpdateTags: '10239',
  DeleteTags: '10240',
  ViewTags: '10241',

  CreateBillingCycle: '7021',
  UpdateBillingCycle: '7022',
  DeleteBillingCycle: '7023',
  ViewBillingCycle: '7024',

  CreateCurrency: '7025',
  UpdateCurrency: '7026',
  DeleteCurrency: '7027',
  ViewCurrency: '7028',

  CreateTnCDocuments: 'CreateTnCDocuments',
  UpdateTnCDocuments: 'UpdateTnCDocuments',
  DeleteTnCDocuments: 'DeleteTnCDocuments',
  ViewTnCDocuments: 'ViewTnCDocuments',

  ViewObservability: 'ViewObservability',

  CreateAdminSettings: '6010',
  UpdateAdminSettings: '6011',
  DeleteAdminSettings: '6012',
  ViewAdminSettings: '6013',

  StartProvisioning: '6014',

  //tenant history
  CreateTenantHistory: '6015',
  ViewTenantHistory: '6016',
  UpdateTenantHistory: '6017',
  DeleteTenantHistory: '6018',

  CreatePlanHistory: '6019',
  ViewPlanHistory: '6020',
  UpdatePlanHistory: '6021',
  DeletePlanHistory: '6022',

  ViewPlanSizes: '7029',
  CreatePlanSizes: '7030',
  UpdatePlanSizes: '7031',
  DeletePlanSizes: '7032',

  CreateConfigureDevices: '6023',
  ViewConfigureDevices: '6024',
  UpdateConfigureDevices: '6025',
  DeleteConfigureDevices: '6026',

  ViewTenantBillings: '6027',

  //tenant deployment history
  CreateTenantDeploymentHistory: 'CreateTenantDeploymentHistory',
  ViewTenantDeploymentHistory: 'ViewTenantDeploymentHistory',
  UpdateTenantDeploymentHistory: 'UpdateTenantDeploymentHistory',
  DeleteTenantDeploymentHistory: 'DeleteTenantDeploymentHistory',
  Admin: '*',
  SendOtp: '5334',
  VerifyOtp: '5335',

  UpdatePassword: '7005',
};
